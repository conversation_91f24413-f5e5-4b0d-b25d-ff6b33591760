import axios from 'axios';
import { cacheService } from './cache';
import { redisKeyPrefix } from '../utils/constants';
import { OrgApiConfig } from '../types/custom';

const forwardLoginRequest = async (
    payload: Record<string, any>
): Promise<{ status: number; data: any }> => {
    try {
        const { org_id } = payload;
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy :: id = ${payload.id} :: org_id = ${org_id}`
        );
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy :: redisKeyPrefix.TENANT_KEYS = ${redisKeyPrefix?.TENANT_KEYS}`
        );
        const tenantsDetails = await cacheService.getMatchingKeysData(
            redisKeyPrefix?.TENANT_KEYS
        );
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy :: tenantsDetails = ${JSON.stringify(
                tenantsDetails
            )}`
        );
        if (!tenantsDetails) {
            console.log(
                `forwardLoginRequest :: /login/v1 from Auth Proxy No Tenants Found`
            );
            return { status: 500, data: { error: 'No Tenants Found' } };
        }
        let matchingTenants;
        for (const key in tenantsDetails) {
            const tenant = tenantsDetails[key];
            const { child_orgs, org_id: tenantOrgId } = tenant;
            if (
                (Array.isArray(child_orgs) && child_orgs.includes(org_id)) ||
                tenantOrgId == org_id
            ) {
                matchingTenants = tenant;
            }
        }
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy :: matchingTenants = ${JSON.stringify(
                matchingTenants
            )}`
        );
        if (!matchingTenants) {
            // default to main tenant
            matchingTenants =
                tenantsDetails[
                    'tenant_id_13fbde52-a112-4b46-8a7d-71355283ce3f'
                ];
        }
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy :: matchingTenants = ${JSON.stringify(
                matchingTenants
            )}`
        );
        const apiMap: OrgApiConfig = {
            api: matchingTenants?.app_url || '',
            auth: matchingTenants?.auth_url || '',
        };
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy to core auth :: apiMap = ${JSON.stringify(
                apiMap
            )}`
        );
        const coreAuthResp = await axios.post(
            `${apiMap.auth}/login/v1`,
            payload
        );
        return {
            status: coreAuthResp.status,
            data: coreAuthResp.data,
        };
    } catch (error: any) {
        console.log(
            `forwardLoginRequest :: /login/v1 from Auth Proxy Api Call :: Error :: ${error?.message}`
        );
        const status = error?.response?.status || 500;
        const data = error?.response?.data || {
            error: 'Internal Server Error',
        };
        return { status, data };
    }
};

export const authProxy = {
    forwardLoginRequest,
};
