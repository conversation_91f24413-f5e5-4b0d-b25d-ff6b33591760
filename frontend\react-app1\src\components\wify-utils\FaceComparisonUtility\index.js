import React from 'react';
import { message } from 'antd';
import AWS from 'aws-sdk';
import http_utils from '../../../util/http_utils';

// AWS Configuration - same as FaceComparisonDemo
AWS.config.update({
    region: 'ap-south-1',
    accessKeyId: '********************',
    secretAccessKey: '9cnbd6RYgvGN73l2dfi/I1P5gqlYUmW3nJfbYLLA',
});
const S3 = new AWS.S3();
const rekognition = new AWS.Rekognition();

const REKOGNITION_CONFIG = {
    similarityThreshold: 70, // Minimum similarity percentage (0-100)
    maxFaces: 10, // Maximum number of faces to detect
    qualityFilter: 'AUTO', // AUTO, LOW, MEDIUM, HIGH
};

/**
 * Face Comparison Utility Class
 * Provides methods to compare faces using AWS Rekognition
 */
class FaceComparisonUtility {
    /**
     * Convert file to base64
     * @param {File|Blob} file - The file to convert
     * @returns {Promise<string>} Base64 string
     */
    static fileToBase64(file) {
        return new Promise((resolve, reject) => {
            // Enhanced file type validation with fallback for S3 downloads
            let isValidImageType = false;

            if (file.type && file.type.startsWith('image/')) {
                // Standard image MIME types
                isValidImageType = true;
                console.log(`✅ Valid image MIME type: ${file.type}`);
            } else if (
                file.type === 'binary/octet-stream' ||
                file.type === 'application/octet-stream' ||
                !file.type ||
                file.type === ''
            ) {
                // S3 downloads often come as binary/octet-stream
                // We'll validate the actual format from the file content
                console.log(
                    `⚠️ Generic MIME type detected: ${file.type || 'empty'}, will validate from content`
                );
                isValidImageType = true;
            } else {
                reject(
                    new Error(
                        `Invalid file type: ${file.type}. Expected image/* or binary/octet-stream.`
                    )
                );
                return;
            }

            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const result = reader.result;
                    if (!result || typeof result !== 'string') {
                        throw new Error('Failed to read file as data URL');
                    }

                    // Enhanced data URL validation
                    let base64;

                    if (result.startsWith('data:image/')) {
                        // Standard image data URL
                        base64 = result.split(',')[1];
                        console.log(`✅ Standard image data URL detected`);
                    } else if (
                        result.startsWith('data:application/octet-stream') ||
                        result.startsWith('data:binary/octet-stream') ||
                        result.startsWith('data:,')
                    ) {
                        // Generic binary data URL (common for S3 downloads)
                        base64 = result.split(',')[1];
                        console.log(
                            `⚠️ Generic binary data URL detected, will validate image format from content`
                        );
                    } else {
                        throw new Error(
                            `Invalid data URL format: ${result.substring(0, 50)}...`
                        );
                    }

                    if (!base64) {
                        throw new Error(
                            'Failed to extract base64 data from data URL'
                        );
                    }

                    // For generic MIME types, validate the actual image format from content
                    if (
                        file.type === 'binary/octet-stream' ||
                        file.type === 'application/octet-stream' ||
                        !file.type ||
                        file.type === ''
                    ) {
                        console.log(
                            '🔍 Validating image format from file content...'
                        );
                        const bytes = this.base64ToUint8Array(base64);
                        const detectedFormat = this.validateImageFormat(bytes);

                        if (!detectedFormat) {
                            const firstBytes = Array.from(bytes.slice(0, 12))
                                .map(
                                    (b) =>
                                        '0x' + b.toString(16).padStart(2, '0')
                                )
                                .join(' ');
                            throw new Error(
                                `File content is not a valid image format. First 12 bytes: ${firstBytes}`
                            );
                        }

                        console.log(
                            `✅ Detected valid image format: ${detectedFormat}`
                        );
                    }

                    console.log(
                        `Successfully converted ${file.type || 'unknown'} to base64, length: ${base64.length}`
                    );
                    resolve(base64);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => {
                reject(new Error('FileReader error occurred'));
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * Convert base64 string to Uint8Array
     * @param {string} base64 - Base64 string
     * @returns {Uint8Array} Byte array
     */
    static base64ToUint8Array(base64) {
        try {
            // Decode base64 to binary string
            const binaryString = atob(base64);

            // Convert binary string to Uint8Array
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            return bytes;
        } catch (error) {
            throw new Error(
                `Failed to convert base64 to Uint8Array: ${error.message}`
            );
        }
    }

    /**
     * Validate image format by checking magic bytes
     * @param {Uint8Array} bytes - Image bytes
     * @returns {string|null} Image format or null if invalid
     */
    static validateImageFormat(bytes) {
        if (bytes.length < 4) return null;

        // Check for common image formats
        // JPEG: FF D8 FF
        if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
            return 'JPEG';
        }

        // PNG: 89 50 4E 47
        if (
            bytes[0] === 0x89 &&
            bytes[1] === 0x50 &&
            bytes[2] === 0x4e &&
            bytes[3] === 0x47
        ) {
            return 'PNG';
        }

        // GIF: 47 49 46 38
        if (
            bytes[0] === 0x47 &&
            bytes[1] === 0x49 &&
            bytes[2] === 0x46 &&
            bytes[3] === 0x38
        ) {
            return 'GIF';
        }

        // WebP: 52 49 46 46 (RIFF) + WebP signature
        if (
            bytes[0] === 0x52 &&
            bytes[1] === 0x49 &&
            bytes[2] === 0x46 &&
            bytes[3] === 0x46 &&
            bytes.length >= 12 &&
            bytes[8] === 0x57 &&
            bytes[9] === 0x45 &&
            bytes[10] === 0x42 &&
            bytes[11] === 0x50
        ) {
            return 'WebP';
        }

        return null;
    }

    /**
     * Convert WebP image to JPEG format using Canvas with improved error handling
     * @param {string} webpBase64 - WebP image as base64 string
     * @returns {Promise<string>} JPEG image as base64 string
     */
    static async convertWebPToJPEG(webpBase64) {
        return new Promise((resolve, reject) => {
            try {
                // Validate input
                if (!webpBase64 || typeof webpBase64 !== 'string') {
                    throw new Error('Invalid WebP base64 input');
                }

                // Create an image element
                const img = new Image();

                // Set up timeout for image loading
                const timeout = setTimeout(() => {
                    reject(
                        new Error('WebP image loading timeout (10 seconds)')
                    );
                }, 10000);

                img.onload = () => {
                    clearTimeout(timeout);
                    try {
                        // Validate image dimensions
                        if (img.width === 0 || img.height === 0) {
                            throw new Error('Invalid image dimensions');
                        }

                        // Check for reasonable size limits
                        if (img.width > 4096 || img.height > 4096) {
                            console.warn(
                                'Large image dimensions:',
                                img.width,
                                'x',
                                img.height
                            );
                        }

                        // Create a canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        if (!ctx) {
                            throw new Error('Failed to get canvas 2D context');
                        }

                        // Set canvas dimensions to match image
                        canvas.width = img.width;
                        canvas.height = img.height;

                        // Clear canvas and draw the image
                        ctx.clearRect(0, 0, canvas.width, canvas.height);
                        ctx.drawImage(img, 0, 0);

                        // Convert to JPEG base64 (quality 0.9)
                        const jpegBase64 = canvas.toDataURL('image/jpeg', 0.9);

                        // Validate the result
                        if (
                            !jpegBase64 ||
                            !jpegBase64.startsWith('data:image/jpeg;base64,')
                        ) {
                            throw new Error(
                                'Canvas toDataURL failed to produce valid JPEG'
                            );
                        }

                        // Remove the data:image/jpeg;base64, prefix
                        const base64Data = jpegBase64.split(',')[1];

                        if (!base64Data || base64Data.length === 0) {
                            throw new Error(
                                'Failed to extract base64 data from canvas output'
                            );
                        }

                        console.log(
                            'convertWebPToJPEG :: Conversion successful',
                            `Original: ${webpBase64.length} chars, JPEG: ${base64Data.length} chars`,
                            `Dimensions: ${img.width}x${img.height}`
                        );

                        resolve(base64Data);
                    } catch (canvasError) {
                        console.error(
                            'convertWebPToJPEG :: Canvas error:',
                            canvasError
                        );
                        reject(
                            new Error(
                                `Canvas conversion failed: ${canvasError.message}`
                            )
                        );
                    }
                };

                img.onerror = (imgError) => {
                    clearTimeout(timeout);
                    console.error(
                        'convertWebPToJPEG :: Image load error:',
                        imgError
                    );
                    reject(
                        new Error(
                            'Failed to load WebP image for conversion. The image may be corrupted or in an unsupported WebP format.'
                        )
                    );
                };

                // Enable CORS for cross-origin images
                img.crossOrigin = 'anonymous';

                // Set the image source to the WebP base64 data
                img.src = `data:image/webp;base64,${webpBase64}`;
            } catch (error) {
                console.error('convertWebPToJPEG :: General error:', error);
                reject(
                    new Error(
                        `WebP to JPEG conversion failed: ${error.message}`
                    )
                );
            }
        });
    }

    /**
     * Get signed URL for S3 object using backend API
     * @param {string} s3Key - S3 object key
     * @returns {Promise<string>} Signed URL
     */
    static async getSignedUrlForS3Object(s3Key) {
        try {
            const response = await fetch(
                `${http_utils.getFullApiServerLink()}/media/signed-url?file_name=${encodeURIComponent(s3Key)}`,
                {
                    method: 'GET',
                    headers: {
                        Authorization: http_utils.getAuthToken(),
                        'Content-Type': 'application/json',
                    },
                }
            );

            if (!response.ok) {
                throw new Error(
                    `Failed to get signed URL: ${response.status} ${response.statusText}`
                );
            }

            const data = await response.json();
            return data.url;
        } catch (error) {
            console.error('Error getting signed URL:', error);
            throw new Error(
                `Failed to get signed URL for ${s3Key}: ${error.message}`
            );
        }
    }

    /**
     * Convert S3 path to full URL with authentication
     * Uses signed URLs for better security and reliability
     * @param {string} path - S3 path (e.g., "org_3/image.jpg") or full URL
     * @returns {Promise<string>} Full URL with authentication
     */
    static async getAuthenticatedImageUrl(path) {
        // If it's already a full URL, return as is
        if (path.startsWith('http://') || path.startsWith('https://')) {
            return path;
        }

        try {
            // Use signed URL for better reliability
            return await this.getSignedUrlForS3Object(path);
        } catch (error) {
            console.warn(
                'Could not create signed URL, falling back to auth token method:',
                error
            );

            try {
                // Fallback to the original auth token method
                const prefixDomain = http_utils.getCDNDomain(); // returns fullApiServerLink + '/media'
                const authToken = http_utils.getAuthToken(); // returns 'Bearer ' + token

                let modifiedUrl = `${prefixDomain}/${path}`;

                if (authToken) {
                    modifiedUrl = `${modifiedUrl}?authorization=${encodeURI(authToken)}`;
                }

                return modifiedUrl;
            } catch (fallbackError) {
                console.warn(
                    'Fallback URL creation also failed:',
                    fallbackError
                );
                // Final fallback to basic URL construction
                const baseUrl = window.location.origin;
                return `${baseUrl}/media/${path}`;
            }
        }
    }

    /**
     * Convert URL to base64 with improved error handling and CORS support
     * @param {string} url - The image URL or S3 path
     * @returns {Promise<string>} Base64 string
     */
    static async urlToBase64(url) {
        try {
            // Convert S3 path to authenticated URL
            const imageUrl = await this.getAuthenticatedImageUrl(url);
            console.log('Fetching image from URL:', imageUrl);

            const response = await fetch(imageUrl, {
                method: 'GET',
                headers: {
                    Accept: 'image/*',
                    'Cache-Control': 'no-cache',
                },
                mode: 'cors', // Explicitly set CORS mode
            });

            if (!response.ok) {
                throw new Error(
                    `Failed to fetch image from ${imageUrl}: ${response.status} ${response.statusText}`
                );
            }

            const contentType = response.headers.get('content-type');
            if (!contentType || !contentType.startsWith('image/')) {
                console.warn(
                    `Unexpected content type: ${contentType}, but proceeding with image processing`
                );
            }

            const blob = await response.blob();

            // Validate blob size
            if (blob.size === 0) {
                throw new Error('Received empty image data');
            }

            if (blob.size > 5 * 1024 * 1024) {
                // 5MB limit for AWS Rekognition
                throw new Error(
                    'Image size exceeds 5MB limit for AWS Rekognition'
                );
            }

            console.log(
                `Successfully fetched image blob: ${blob.size} bytes, type: ${blob.type}`
            );

            // Enhanced logging for debugging
            console.log('🔄 Converting blob to base64...');
            const base64Result = await this.fileToBase64(blob);
            console.log(
                `✅ Successfully converted blob to base64, length: ${base64Result.length}`
            );

            return base64Result;
        } catch (error) {
            console.error('Error in urlToBase64:', error);
            throw new Error(
                `Failed to convert URL to base64: ${error.message}`
            );
        }
    }

    /**
     * Download and encode image from S3 with improved error handling
     * @param {string} objectKey - S3 object key
     * @returns {Promise<string>} Base64 encoded image
     */
    static async downloadAndEncodeImage({ objectKey }) {
        try {
            const decodedObjectKey = decodeURIComponent(objectKey);
            console.log(
                'downloadAndEncodeImage :: decodedObjectKey',
                decodedObjectKey
            );

            // Try using the URL-based approach first (more reliable)
            try {
                console.log(
                    'downloadAndEncodeImage :: Attempting URL-based fetch'
                );
                return await this.urlToBase64(decodedObjectKey);
            } catch (urlError) {
                console.warn(
                    'downloadAndEncodeImage :: URL-based fetch failed, falling back to direct S3:',
                    urlError
                );
            }

            // Fallback to direct S3 access
            const s3 = new AWS.S3();
            const s3Response = await s3
                .getObject({
                    Bucket: 'tms-media' || process.env.S3_BUCKET,
                    Key: decodedObjectKey,
                })
                .promise();

            console.log(
                'downloadAndEncodeImage :: s3Response received, ContentLength:',
                s3Response.ContentLength
            );

            const body = s3Response.Body;
            if (!body) {
                throw new Error('S3 response body is empty');
            }

            // Validate content length
            if (s3Response.ContentLength === 0) {
                throw new Error('S3 object is empty');
            }

            if (s3Response.ContentLength > 5 * 1024 * 1024) {
                throw new Error(
                    'Image size exceeds 5MB limit for AWS Rekognition'
                );
            }

            // Convert S3 Body to base64 with improved handling
            let base64Image;

            if (typeof Buffer !== 'undefined' && Buffer.isBuffer(body)) {
                // Node.js Buffer (most reliable)
                base64Image = body.toString('base64');
                console.log(
                    'downloadAndEncodeImage :: Used Buffer.toString method'
                );
            } else if (body instanceof Uint8Array) {
                // Uint8Array - use more efficient conversion
                const binaryString = Array.from(body, (byte) =>
                    String.fromCharCode(byte)
                ).join('');
                base64Image = btoa(binaryString);
                console.log(
                    'downloadAndEncodeImage :: Used Uint8Array conversion'
                );
            } else if (body && typeof body.data !== 'undefined') {
                // Buffer-like object with data property
                const uint8Array = new Uint8Array(body.data);
                const binaryString = Array.from(uint8Array, (byte) =>
                    String.fromCharCode(byte)
                ).join('');
                base64Image = btoa(binaryString);
                console.log(
                    'downloadAndEncodeImage :: Used data property conversion'
                );
            } else {
                throw new Error(
                    `Unsupported S3 response body format: ${typeof body}, constructor: ${body?.constructor?.name}`
                );
            }

            if (!base64Image || base64Image.length === 0) {
                throw new Error('Base64 conversion resulted in empty string');
            }

            console.log(
                'downloadAndEncodeImage :: base64Image length:',
                base64Image.length
            );

            // Validate and process the image
            const processedBase64 =
                await this.validateAndProcessImage(base64Image);
            return processedBase64;
        } catch (error) {
            console.error(
                'FaceComparisonUtility :: Error in downloadAndEncodeImage:',
                error
            );
            throw new Error(
                `Failed to download and encode image: ${error.message}`
            );
        }
    }

    /**
     * Validate and process image data
     * @param {string} base64Image - Base64 encoded image
     * @returns {Promise<string>} Processed base64 image
     */
    static async validateAndProcessImage(base64Image) {
        try {
            // Validate base64 format
            if (!/^[A-Za-z0-9+/]*={0,2}$/.test(base64Image)) {
                throw new Error('Invalid base64 format');
            }

            const testBytes = this.base64ToUint8Array(base64Image);
            const format = this.validateImageFormat(testBytes);

            console.log('validateAndProcessImage :: detected format:', format);
            console.log(
                'validateAndProcessImage :: image size:',
                testBytes.length,
                'bytes'
            );
            console.log(
                'validateAndProcessImage :: first 12 bytes:',
                Array.from(testBytes.slice(0, 12))
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' ')
            );

            if (!format) {
                throw new Error(
                    'Unsupported or corrupted image format. First 12 bytes: ' +
                        Array.from(testBytes.slice(0, 12))
                            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                            .join(' ')
                );
            }

            // Convert WebP to JPEG if needed (AWS Rekognition doesn't support WebP)
            if (format === 'WebP') {
                console.log(
                    'validateAndProcessImage :: Converting WebP to JPEG for AWS Rekognition compatibility'
                );
                const convertedBase64 =
                    await this.convertWebPToJPEG(base64Image);

                // Validate the converted image
                const convertedBytes = this.base64ToUint8Array(convertedBase64);
                const convertedFormat =
                    this.validateImageFormat(convertedBytes);

                if (convertedFormat !== 'JPEG') {
                    throw new Error(
                        `WebP to JPEG conversion failed. Result format: ${convertedFormat}`
                    );
                }

                console.log(
                    'validateAndProcessImage :: WebP successfully converted to JPEG'
                );
                return convertedBase64;
            }

            return base64Image;
        } catch (error) {
            console.error('validateAndProcessImage :: Error:', error);
            throw new Error(
                `Image validation/processing failed: ${error.message}`
            );
        }
    }

    /**
     * Compare two faces using AWS Rekognition
     * @param {File|Blob|string} sourceImage - Source image (File/Blob or URL)
     * @param {File|Blob|string} targetImage - Target image (File/Blob or URL)
     * @param {number} similarityThreshold - Minimum similarity threshold (default: 70)
     * @returns {Promise<Object>} Comparison result
     */
    static async compareFaces(
        sourceImage,
        targetImage,
        similarityThreshold = REKOGNITION_CONFIG.similarityThreshold
    ) {
        try {
            console.log(
                'Face comparison - Source image type:',
                typeof sourceImage,
                sourceImage
            );
            console.log(
                'Face comparison - Target image type:',
                typeof targetImage,
                targetImage
            );

            // Convert images to base64 with improved processing
            let sourceBase64, targetBase64;

            console.log('Processing source image...');
            if (typeof sourceImage === 'string') {
                // URL/S3 path
                console.log('Converting source URL to base64:', sourceImage);
                sourceBase64 = await this.downloadAndEncodeImage({
                    objectKey: sourceImage,
                });
            } else {
                // File/Blob
                console.log('Converting source File/Blob to base64');
                sourceBase64 = await this.fileToBase64(sourceImage);
                // Process the file-based image through validation
                sourceBase64 = await this.validateAndProcessImage(sourceBase64);
            }

            console.log('Processing target image...');
            if (typeof targetImage === 'string') {
                // URL/S3 path
                console.log('Converting target URL to base64:', targetImage);
                targetBase64 = await this.downloadAndEncodeImage({
                    objectKey: targetImage,
                });
            } else {
                // File/Blob
                console.log('Converting target File/Blob to base64');
                targetBase64 = await this.fileToBase64(targetImage);
                // Process the file-based image through validation
                targetBase64 = await this.validateAndProcessImage(targetBase64);
            }

            console.log(
                'Base64 conversion completed, preparing for AWS Rekognition...'
            );

            // Final validation of processed images
            const sourceBytes = this.base64ToUint8Array(sourceBase64);
            const targetBytes = this.base64ToUint8Array(targetBase64);

            const sourceFormat = this.validateImageFormat(sourceBytes);
            const targetFormat = this.validateImageFormat(targetBytes);

            console.log(
                'Final source image format:',
                sourceFormat,
                'Size:',
                sourceBytes.length,
                'bytes'
            );
            console.log(
                'Final target image format:',
                targetFormat,
                'Size:',
                targetBytes.length,
                'bytes'
            );

            // Validate image sizes (AWS Rekognition has limits)
            if (sourceBytes.length === 0 || targetBytes.length === 0) {
                throw new Error(
                    'One or both images are empty after processing'
                );
            }

            if (
                sourceBytes.length > 5 * 1024 * 1024 ||
                targetBytes.length > 5 * 1024 * 1024
            ) {
                throw new Error(
                    'Image size too large. AWS Rekognition supports images up to 5MB'
                );
            }

            // Validate minimum image size (AWS Rekognition requirement)
            if (sourceBytes.length < 80 || targetBytes.length < 80) {
                throw new Error(
                    'Image size too small. AWS Rekognition requires images to be at least 80 bytes'
                );
            }

            // Debug: Log first few bytes of each image
            console.log(
                'Source image first 12 bytes:',
                Array.from(sourceBytes.slice(0, 12))
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' ')
            );
            console.log(
                'Target image first 12 bytes:',
                Array.from(targetBytes.slice(0, 12))
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' ')
            );

            // Validate image formats
            if (!sourceFormat) {
                throw new Error(
                    'Source image format is not supported or corrupted. First 12 bytes: ' +
                        Array.from(sourceBytes.slice(0, 12))
                            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                            .join(' ')
                );
            }

            if (!targetFormat) {
                throw new Error(
                    'Target image format is not supported or corrupted. First 12 bytes: ' +
                        Array.from(targetBytes.slice(0, 12))
                            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                            .join(' ')
                );
            }

            // Ensure formats are supported by AWS Rekognition
            const supportedFormats = ['JPEG', 'PNG'];
            if (!supportedFormats.includes(sourceFormat)) {
                throw new Error(
                    `Source image format '${sourceFormat}' is not supported by AWS Rekognition. Supported formats: ${supportedFormats.join(', ')}`
                );
            }

            if (!supportedFormats.includes(targetFormat)) {
                throw new Error(
                    `Target image format '${targetFormat}' is not supported by AWS Rekognition. Supported formats: ${supportedFormats.join(', ')}`
                );
            }

            // Create the parameters for AWS SDK v2 with improved byte handling
            let sourceImageBytes, targetImageBytes;

            // Always use Buffer for consistency and reliability
            if (typeof Buffer !== 'undefined') {
                // Node.js environment or browser with Buffer polyfill
                sourceImageBytes = Buffer.from(sourceBytes);
                targetImageBytes = Buffer.from(targetBytes);
                console.log('Using Buffer for AWS SDK (recommended)');
            } else {
                // Pure browser environment - use Uint8Array
                sourceImageBytes = sourceBytes;
                targetImageBytes = targetBytes;
                console.log('Using Uint8Array for AWS SDK (fallback)');
            }

            // Validate the byte arrays one more time
            if (!sourceImageBytes || sourceImageBytes.length === 0) {
                throw new Error('Source image bytes are empty or invalid');
            }
            if (!targetImageBytes || targetImageBytes.length === 0) {
                throw new Error('Target image bytes are empty or invalid');
            }

            const params = {
                SourceImage: { Bytes: sourceImageBytes },
                TargetImage: { Bytes: targetImageBytes },
                SimilarityThreshold: Math.max(
                    0,
                    Math.min(100, similarityThreshold)
                ), // Ensure valid range
            };

            console.log('AWS Rekognition params prepared:', {
                sourceImageSize: sourceImageBytes.length,
                targetImageSize: targetImageBytes.length,
                similarityThreshold: params.SimilarityThreshold,
                sourceImageType: sourceImageBytes.constructor.name,
                targetImageType: targetImageBytes.constructor.name,
                sourceFormat: sourceFormat,
                targetFormat: targetFormat,
                sourceIsBuffer:
                    typeof Buffer !== 'undefined'
                        ? Buffer.isBuffer(sourceImageBytes)
                        : false,
                targetIsBuffer:
                    typeof Buffer !== 'undefined'
                        ? Buffer.isBuffer(targetImageBytes)
                        : false,
            });

            // Execute the API call using AWS SDK v2
            console.log('Calling AWS Rekognition compareFaces API...');

            // Additional validation before API call
            console.log('Final validation before AWS API call:');
            console.log('- Source image bytes type:', typeof sourceImageBytes);
            console.log('- Target image bytes type:', typeof targetImageBytes);
            console.log(
                '- Source image bytes length:',
                sourceImageBytes.length
            );
            console.log(
                '- Target image bytes length:',
                targetImageBytes.length
            );
            console.log('- Similarity threshold:', params.SimilarityThreshold);

            // Validate that we have actual byte data
            if (sourceImageBytes.length === 0) {
                throw new Error('Source image bytes array is empty');
            }
            if (targetImageBytes.length === 0) {
                throw new Error('Target image bytes array is empty');
            }

            // Log the actual parameter structure (without the binary data)
            console.log('AWS Rekognition params structure:', {
                SourceImage: {
                    Bytes: `[${sourceImageBytes.constructor.name} with ${sourceImageBytes.length} bytes]`,
                },
                TargetImage: {
                    Bytes: `[${targetImageBytes.constructor.name} with ${targetImageBytes.length} bytes]`,
                },
                SimilarityThreshold: params.SimilarityThreshold,
            });

            const response = await rekognition.compareFaces(params).promise();

            return {
                success: true,
                data: response,
                hasMatch:
                    response.FaceMatches && response.FaceMatches.length > 0,
                similarity:
                    response.FaceMatches && response.FaceMatches.length > 0
                        ? response.FaceMatches[0].Similarity
                        : 0,
                matchCount: response.FaceMatches
                    ? response.FaceMatches.length
                    : 0,
            };
        } catch (error) {
            console.error('Face comparison error:', error);

            // Enhanced error logging for debugging
            const errorDetails = {
                message: error.message,
                code: error.code,
                statusCode: error.statusCode,
                requestId: error.requestId,
                retryable: error.retryable,
                time: new Date().toISOString(),
                // Add more context for InvalidParameterException
                sourceImageType: typeof sourceImage,
                targetImageType: typeof targetImage,
                sourceImageSize:
                    sourceImage instanceof File || sourceImage instanceof Blob
                        ? sourceImage.size
                        : 'N/A',
                targetImageSize:
                    targetImage instanceof File || targetImage instanceof Blob
                        ? targetImage.size
                        : 'N/A',
                similarityThreshold: similarityThreshold,
            };

            console.error('Detailed error information:', errorDetails);

            // Log additional debugging info for InvalidParameterException
            if (error.code === 'InvalidParameterException') {
                console.error('🚨 InvalidParameterException Debug Info:');
                console.error('- AWS Region:', AWS.config.region);
                console.error(
                    '- AWS Access Key ID:',
                    AWS.config.accessKeyId
                        ? AWS.config.accessKeyId.substring(0, 8) + '...'
                        : 'Not set'
                );
                console.error('- Source Image Info:', {
                    type: typeof sourceImage,
                    isFile: sourceImage instanceof File,
                    isBlob: sourceImage instanceof Blob,
                    isString: typeof sourceImage === 'string',
                    size:
                        sourceImage instanceof File ||
                        sourceImage instanceof Blob
                            ? sourceImage.size
                            : 'N/A',
                });
                console.error('- Target Image Info:', {
                    type: typeof targetImage,
                    isFile: targetImage instanceof File,
                    isBlob: targetImage instanceof Blob,
                    isString: typeof targetImage === 'string',
                    size:
                        targetImage instanceof File ||
                        targetImage instanceof Blob
                            ? targetImage.size
                            : 'N/A',
                });

                // Suggest debugging steps
                console.error('🔧 Debugging suggestions:');
                console.error(
                    '1. Run FaceComparisonUtility.debugFaceComparisonIssue(sourceImage, targetImage)'
                );
                console.error(
                    '2. Check console logs for image processing steps'
                );
                console.error('3. Verify AWS credentials and permissions');
                console.error('4. Ensure images are valid JPEG/PNG under 5MB');
            }

            // Provide more specific error messages based on AWS error codes
            let userFriendlyMessage = error.message;

            if (error.code === 'InvalidParameterException') {
                userFriendlyMessage =
                    'Invalid image parameters. Please ensure images are valid JPEG or PNG files under 5MB.';
            } else if (error.code === 'InvalidImageFormatException') {
                userFriendlyMessage =
                    'Invalid image format. Please use JPEG or PNG images.';
            } else if (error.code === 'ImageTooLargeException') {
                userFriendlyMessage =
                    'Image is too large. Please use images under 5MB.';
            } else if (error.code === 'InvalidS3ObjectException') {
                userFriendlyMessage =
                    'Could not access the image file. Please check if the image exists and is accessible.';
            } else if (error.code === 'AccessDeniedException') {
                userFriendlyMessage =
                    'Access denied. Please check AWS credentials and permissions.';
            }

            return {
                success: false,
                error: userFriendlyMessage,
                errorCode: error.code,
                errorDetails: errorDetails,
                hasMatch: false,
                similarity: 0,
                matchCount: 0,
            };
        }
    }

    /**
     * Validate face comparison result
     * @param {Object} result - Result from compareFaces
     * @param {number} minSimilarity - Minimum required similarity (default: 70)
     * @returns {Object} Validation result
     */
    static validateFaceComparison(
        result,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        if (!result.success) {
            return {
                isValid: false,
                message: `Face comparison failed: ${result.error}`,
                similarity: 0,
            };
        }

        if (!result.hasMatch) {
            return {
                isValid: false,
                message: 'No matching faces found between the images',
                similarity: 0,
            };
        }

        if (result.similarity < minSimilarity) {
            return {
                isValid: false,
                message: `Face similarity (${result.similarity.toFixed(1)}%) is below required threshold (${minSimilarity}%)`,
                similarity: result.similarity,
            };
        }

        return {
            isValid: true,
            message: `Face verification successful (${result.similarity.toFixed(1)}% similarity)`,
            similarity: result.similarity,
        };
    }

    /**
     * Compare camera field images with profile picture
     * @param {Object} cameraFiles - Camera files object with sections
     * @param {Array} verificationFields - Array of field keys to verify
     * @param {string} profilePictureUrl - Profile picture URL
     * @param {number} minSimilarity - Minimum required similarity
     * @returns {Promise<Object>} Verification result
     */
    static async verifyCameraFieldsWithProfile(
        cameraFiles,
        verificationFields,
        profilePictureUrl,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        console.log('verifyCameraFieldsWithProfile', cameraFiles);
        console.log('verifyCameraFieldsWithProfile', verificationFields);
        console.log('verifyCameraFieldsWithProfile', profilePictureUrl);
        console.log('verifyCameraFieldsWithProfile', minSimilarity);
        if (!profilePictureUrl) {
            return {
                success: false,
                message:
                    'Profile picture not found. Please upload a profile picture first.',
                results: [],
            };
        }

        if (!verificationFields || verificationFields.length === 0) {
            return {
                success: true,
                message: 'No verification fields specified',
                results: [],
            };
        }

        const results = [];
        let allValid = true;
        let errorMessages = [];

        for (const fieldKey of verificationFields) {
            const fieldFiles = cameraFiles[fieldKey];

            if (!fieldFiles || fieldFiles.length === 0) {
                allValid = false;
                errorMessages.push(`No image found for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `No image found for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            // Use the first image from the field
            const firstFile = fieldFiles[0];
            let imageToCompare;

            if (typeof firstFile === 'string') {
                // Camera files are stored as S3 paths (strings)
                imageToCompare = firstFile;
            } else if (firstFile && firstFile.url) {
                // Use the URL if available (object format)
                imageToCompare = firstFile.url;
            } else if (firstFile && firstFile.file) {
                // Use the file object
                imageToCompare = firstFile.file;
            } else if (firstFile) {
                // Fallback to the file itself
                imageToCompare = firstFile;
            } else {
                allValid = false;
                errorMessages.push(`Invalid image data for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `Invalid image data for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            try {
                const comparisonResult = await this.compareFaces(
                    imageToCompare,
                    profilePictureUrl,
                    minSimilarity
                );
                const validation = this.validateFaceComparison(
                    comparisonResult,
                    minSimilarity
                );

                results.push({
                    fieldKey,
                    success: validation.isValid,
                    message: validation.message,
                    similarity: validation.similarity,
                    comparisonData: comparisonResult.data,
                });

                if (!validation.isValid) {
                    allValid = false;
                    errorMessages.push(`${fieldKey}: ${validation.message}`);
                }
            } catch (error) {
                allValid = false;
                const errorMsg = `Face comparison failed for ${fieldKey}: ${error.message}`;
                errorMessages.push(errorMsg);
                results.push({
                    fieldKey,
                    success: false,
                    message: errorMsg,
                    similarity: 0,
                });
            }
        }

        return {
            success: allValid,
            message: allValid
                ? 'All face verifications passed successfully'
                : `Face verification failed: ${errorMessages.join('; ')}`,
            results,
            errorMessages,
        };
    }

    /**
     * Debug utility to test image processing pipeline
     * @param {File|Blob|string} image - Image to test
     * @returns {Promise<Object>} Debug information
     */
    static async debugImageProcessing(image) {
        const debugInfo = {
            inputType: typeof image,
            inputSize:
                image instanceof File || image instanceof Blob
                    ? image.size
                    : 'N/A',
            inputName: image instanceof File ? image.name : 'N/A',
            steps: [],
            errors: [],
            success: false,
        };

        try {
            debugInfo.steps.push('Starting image processing debug...');

            // Step 1: Convert to base64
            let base64;
            if (typeof image === 'string') {
                debugInfo.steps.push('Processing S3 path/URL...');
                base64 = await this.downloadAndEncodeImage({
                    objectKey: image,
                });
            } else {
                debugInfo.steps.push('Processing File/Blob...');
                base64 = await this.fileToBase64(image);
                base64 = await this.validateAndProcessImage(base64);
            }

            debugInfo.base64Length = base64.length;
            debugInfo.steps.push(
                `Base64 conversion successful: ${base64.length} characters`
            );

            // Step 2: Convert to bytes and validate format
            const bytes = this.base64ToUint8Array(base64);
            const format = this.validateImageFormat(bytes);

            debugInfo.imageFormat = format;
            debugInfo.imageSizeBytes = bytes.length;
            debugInfo.firstBytes = Array.from(bytes.slice(0, 12))
                .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                .join(' ');

            debugInfo.steps.push(`Image format detected: ${format}`);
            debugInfo.steps.push(`Image size: ${bytes.length} bytes`);

            // Step 3: Validate AWS Rekognition compatibility
            const supportedFormats = ['JPEG', 'PNG'];
            const isFormatSupported = supportedFormats.includes(format);
            const isSizeValid =
                bytes.length >= 80 && bytes.length <= 5 * 1024 * 1024;

            debugInfo.isFormatSupported = isFormatSupported;
            debugInfo.isSizeValid = isSizeValid;
            debugInfo.awsRekognitionCompatible =
                isFormatSupported && isSizeValid;

            if (debugInfo.awsRekognitionCompatible) {
                debugInfo.steps.push(
                    '✅ Image is compatible with AWS Rekognition'
                );
                debugInfo.success = true;
            } else {
                if (!isFormatSupported) {
                    debugInfo.errors.push(
                        `Unsupported format: ${format}. Supported: ${supportedFormats.join(', ')}`
                    );
                }
                if (!isSizeValid) {
                    debugInfo.errors.push(
                        `Invalid size: ${bytes.length} bytes. Must be 80 bytes to 5MB`
                    );
                }
                debugInfo.steps.push(
                    '❌ Image is NOT compatible with AWS Rekognition'
                );
            }
        } catch (error) {
            debugInfo.errors.push(error.message);
            debugInfo.steps.push(`❌ Error: ${error.message}`);
        }

        return debugInfo;
    }

    /**
     * Test AWS Rekognition connection and credentials
     * @returns {Promise<Object>} Connection test result
     */
    static async testAWSConnection() {
        try {
            console.log('Testing AWS Rekognition connection...');

            // Try to list collections (minimal operation to test credentials)
            const response = await rekognition
                .listCollections({ MaxResults: 1 })
                .promise();

            return {
                success: true,
                message: 'AWS Rekognition connection successful',
                region: AWS.config.region,
                hasCollections:
                    response.CollectionIds && response.CollectionIds.length > 0,
                collectionsCount: response.CollectionIds
                    ? response.CollectionIds.length
                    : 0,
            };
        } catch (error) {
            return {
                success: false,
                message: `AWS Rekognition connection failed: ${error.message}`,
                errorCode: error.code,
                region: AWS.config.region,
            };
        }
    }

    /**
     * Debug specific face comparison issue
     * @param {File|Blob|string} sourceImage - Source image
     * @param {File|Blob|string} targetImage - Target image
     * @returns {Promise<Object>} Detailed debug information
     */
    static async debugFaceComparisonIssue(sourceImage, targetImage) {
        const debugResult = {
            timestamp: new Date().toISOString(),
            sourceImageDebug: null,
            targetImageDebug: null,
            awsConnectionTest: null,
            recommendations: [],
            success: false,
        };

        try {
            console.log('🔍 Starting comprehensive face comparison debug...');

            // Test AWS connection first
            debugResult.awsConnectionTest = await this.testAWSConnection();
            if (!debugResult.awsConnectionTest.success) {
                debugResult.recommendations.push(
                    'Fix AWS credentials and connection first'
                );
                return debugResult;
            }

            // Debug source image
            console.log('🔍 Debugging source image...');
            debugResult.sourceImageDebug =
                await this.debugImageProcessing(sourceImage);

            // Debug target image
            console.log('🔍 Debugging target image...');
            debugResult.targetImageDebug =
                await this.debugImageProcessing(targetImage);

            // Analyze results and provide recommendations
            if (!debugResult.sourceImageDebug.success) {
                debugResult.recommendations.push(
                    `Source image issue: ${debugResult.sourceImageDebug.errors.join(', ')}`
                );
            }

            if (!debugResult.targetImageDebug.success) {
                debugResult.recommendations.push(
                    `Target image issue: ${debugResult.targetImageDebug.errors.join(', ')}`
                );
            }

            // Check for common issues
            if (
                debugResult.sourceImageDebug.imageFormat === 'WebP' ||
                debugResult.targetImageDebug.imageFormat === 'WebP'
            ) {
                debugResult.recommendations.push(
                    'WebP format detected - ensure conversion to JPEG is working properly'
                );
            }

            if (
                debugResult.sourceImageDebug.imageSizeBytes > 1024 * 1024 ||
                debugResult.targetImageDebug.imageSizeBytes > 1024 * 1024
            ) {
                debugResult.recommendations.push(
                    'Large image sizes detected - consider resizing for better performance'
                );
            }

            // Try a minimal face comparison if both images are valid
            if (
                debugResult.sourceImageDebug.success &&
                debugResult.targetImageDebug.success
            ) {
                try {
                    console.log(
                        '🔍 Attempting minimal face comparison test...'
                    );
                    const testResult = await this.compareFaces(
                        sourceImage,
                        targetImage,
                        50
                    ); // Lower threshold for testing
                    debugResult.faceComparisonTest = {
                        success: testResult.success,
                        error: testResult.error,
                        errorCode: testResult.errorCode,
                        hasMatch: testResult.hasMatch,
                        similarity: testResult.similarity,
                    };

                    if (testResult.success) {
                        debugResult.success = true;
                        debugResult.recommendations.push(
                            '✅ Face comparison is working correctly'
                        );
                    } else {
                        debugResult.recommendations.push(
                            `❌ Face comparison failed: ${testResult.error}`
                        );
                    }
                } catch (comparisonError) {
                    debugResult.faceComparisonTest = {
                        success: false,
                        error: comparisonError.message,
                        errorCode: comparisonError.code,
                    };
                    debugResult.recommendations.push(
                        `❌ Face comparison test failed: ${comparisonError.message}`
                    );
                }
            }

            return debugResult;
        } catch (error) {
            debugResult.error = error.message;
            debugResult.recommendations.push(
                `Debug process failed: ${error.message}`
            );
            return debugResult;
        }
    }

    /**
     * Quick test with sample images to verify the system is working
     * @returns {Promise<Object>} Test result
     */
    static async quickSystemTest() {
        try {
            console.log('🧪 Running quick system test...');

            // Test AWS connection
            const connectionTest = await this.testAWSConnection();
            if (!connectionTest.success) {
                return {
                    success: false,
                    message: 'AWS connection failed',
                    details: connectionTest,
                };
            }

            // Create a simple test image (1x1 pixel JPEG)
            const testImageBase64 =
                '/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A';

            // Convert to blob
            const binaryString = atob(testImageBase64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }
            const testBlob = new Blob([bytes], { type: 'image/jpeg' });

            // Test image processing
            const imageTest = await this.debugImageProcessing(testBlob);

            return {
                success: imageTest.success,
                message: imageTest.success
                    ? 'System test passed'
                    : 'System test failed',
                details: {
                    awsConnection: connectionTest,
                    imageProcessing: imageTest,
                },
            };
        } catch (error) {
            return {
                success: false,
                message: `System test failed: ${error.message}`,
                error: error.message,
            };
        }
    }
}

export default FaceComparisonUtility;
