import React from 'react';
import { message } from 'antd';
import AWS from 'aws-sdk';
import http_utils from '../../../util/http_utils';

// AWS Configuration - same as FaceComparisonDemo
AWS.config.update({
    region: 'ap-south-1',
    accessKeyId: '********************',
    secretAccessKey: '9cnbd6RYgvGN73l2dfi/I1P5gqlYUmW3nJfbYLLA',
});
const S3 = new AWS.S3();
const rekognition = new AWS.Rekognition();

const REKOGNITION_CONFIG = {
    similarityThreshold: 70, // Minimum similarity percentage (0-100)
    maxFaces: 10, // Maximum number of faces to detect
    qualityFilter: 'AUTO', // AUTO, LOW, MEDIUM, HIGH
};

/**
 * Face Comparison Utility Class
 * Provides methods to compare faces using AWS Rekognition
 */
class FaceComparisonUtility {
    /**
     * Convert file to base64
     * @param {File|Blob} file - The file to convert
     * @returns {Promise<string>} Base64 string
     */
    static fileToBase64(file) {
        return new Promise((resolve, reject) => {
            // Validate that it's actually an image
            if (file.type && !file.type.startsWith('image/')) {
                reject(
                    new Error(
                        `Invalid file type: ${file.type}. Expected image.`
                    )
                );
                return;
            }

            const reader = new FileReader();
            reader.onload = () => {
                try {
                    const result = reader.result;
                    if (!result || typeof result !== 'string') {
                        throw new Error('Failed to read file as data URL');
                    }

                    // Validate data URL format
                    if (!result.startsWith('data:image/')) {
                        throw new Error(
                            'Invalid data URL format - not an image'
                        );
                    }

                    const base64 = result.split(',')[1];
                    if (!base64) {
                        throw new Error(
                            'Failed to extract base64 data from data URL'
                        );
                    }

                    console.log(
                        `Successfully converted ${file.type || 'unknown'} to base64, length: ${base64.length}`
                    );
                    resolve(base64);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = () => {
                reject(new Error('FileReader error occurred'));
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * Convert base64 string to Uint8Array
     * @param {string} base64 - Base64 string
     * @returns {Uint8Array} Byte array
     */
    static base64ToUint8Array(base64) {
        try {
            // Decode base64 to binary string
            const binaryString = atob(base64);

            // Convert binary string to Uint8Array
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            return bytes;
        } catch (error) {
            throw new Error(
                `Failed to convert base64 to Uint8Array: ${error.message}`
            );
        }
    }

    /**
     * Validate image format by checking magic bytes
     * @param {Uint8Array} bytes - Image bytes
     * @returns {string|null} Image format or null if invalid
     */
    static validateImageFormat(bytes) {
        if (bytes.length < 4) return null;

        // Check for common image formats
        // JPEG: FF D8 FF
        if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
            return 'JPEG';
        }

        // PNG: 89 50 4E 47
        if (
            bytes[0] === 0x89 &&
            bytes[1] === 0x50 &&
            bytes[2] === 0x4e &&
            bytes[3] === 0x47
        ) {
            return 'PNG';
        }

        // GIF: 47 49 46 38
        if (
            bytes[0] === 0x47 &&
            bytes[1] === 0x49 &&
            bytes[2] === 0x46 &&
            bytes[3] === 0x38
        ) {
            return 'GIF';
        }

        // WebP: 52 49 46 46 (RIFF) + WebP signature
        if (
            bytes[0] === 0x52 &&
            bytes[1] === 0x49 &&
            bytes[2] === 0x46 &&
            bytes[3] === 0x46 &&
            bytes.length >= 12 &&
            bytes[8] === 0x57 &&
            bytes[9] === 0x45 &&
            bytes[10] === 0x42 &&
            bytes[11] === 0x50
        ) {
            return 'WebP';
        }

        return null;
    }

    /**
     * Convert WebP image to JPEG format using Canvas
     * @param {string} webpBase64 - WebP image as base64 string
     * @returns {Promise<string>} JPEG image as base64 string
     */
    static async convertWebPToJPEG(webpBase64) {
        return new Promise((resolve, reject) => {
            try {
                // Create an image element
                const img = new Image();

                img.onload = () => {
                    try {
                        // Create a canvas
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set canvas dimensions to match image
                        canvas.width = img.width;
                        canvas.height = img.height;

                        // Draw the image on canvas
                        ctx.drawImage(img, 0, 0);

                        // Convert to JPEG base64 (quality 0.9)
                        const jpegBase64 = canvas.toDataURL('image/jpeg', 0.9);

                        // Remove the data:image/jpeg;base64, prefix
                        const base64Data = jpegBase64.split(',')[1];

                        console.log(
                            'convertWebPToJPEG :: Conversion successful, JPEG size:',
                            base64Data.length
                        );
                        resolve(base64Data);
                    } catch (canvasError) {
                        console.error(
                            'convertWebPToJPEG :: Canvas error:',
                            canvasError
                        );
                        reject(
                            new Error(
                                `Canvas conversion failed: ${canvasError.message}`
                            )
                        );
                    }
                };

                img.onerror = (imgError) => {
                    console.error(
                        'convertWebPToJPEG :: Image load error:',
                        imgError
                    );
                    reject(
                        new Error('Failed to load WebP image for conversion')
                    );
                };

                // Set the image source to the WebP base64 data
                img.src = `data:image/webp;base64,${webpBase64}`;
            } catch (error) {
                console.error('convertWebPToJPEG :: General error:', error);
                reject(
                    new Error(
                        `WebP to JPEG conversion failed: ${error.message}`
                    )
                );
            }
        });
    }

    /**
     * Convert S3 path to full URL with authentication
     * Uses the same pattern as S3Uploader's getModfiedUrlFrFilePreview
     * @param {string} path - S3 path (e.g., "org_3/image.jpg") or full URL
     * @returns {string} Full URL with authentication
     */
    static getAuthenticatedImageUrl(path) {
        // If it's already a full URL, return as is
        if (path.startsWith('http://') || path.startsWith('https://')) {
            return path;
        }

        try {
            // Use the same pattern as S3Uploader
            const prefixDomain = http_utils.getCDNDomain(); // returns fullApiServerLink + '/media'
            const authToken = http_utils.getAuthToken(); // returns 'Bearer ' + token

            let modifiedUrl = `${prefixDomain}/${path}`;

            if (authToken) {
                modifiedUrl = `${modifiedUrl}?authorization=${encodeURI(authToken)}`;
            }

            return modifiedUrl;
        } catch (error) {
            console.warn('Could not create authenticated URL:', error);
            // Fallback to basic URL construction
            const baseUrl = window.location.origin;
            return `${baseUrl}/media/${path}`;
        }
    }

    /**
     * Convert URL to base64
     * @param {string} url - The image URL or S3 path
     * @returns {Promise<string>} Base64 string
     */
    static urlToBase64(url) {
        return new Promise((resolve, reject) => {
            // Convert S3 path to authenticated URL using the same pattern as S3Uploader
            const imageUrl = this.getAuthenticatedImageUrl(url);

            console.log('Fetching image from URL:', imageUrl);

            fetch(imageUrl, {
                method: 'GET',
                headers: {
                    Accept: 'image/*',
                    'Cache-Control': 'no-cache',
                },
            })
                .then((response) => {
                    if (!response.ok) {
                        throw new Error(
                            `Failed to fetch image from ${imageUrl}: ${response.status} ${response.statusText}`
                        );
                    }

                    const contentType = response.headers.get('content-type');
                    if (!contentType || !contentType.startsWith('image/')) {
                        throw new Error(
                            `Invalid content type: ${contentType}. Expected image.`
                        );
                    }

                    return response.blob();
                })
                .then((blob) => this.fileToBase64(blob))
                .then(resolve)
                .catch(reject);
        });
    }

    static async downloadAndEncodeImage({ objectKey }) {
        try {
            const decodedObjectKey = decodeURIComponent(objectKey);
            console.log(
                'downloadAndEncodeImage :: decodedObjectKey',
                decodedObjectKey
            );

            const s3 = new AWS.S3();

            const s3Response = await s3
                .getObject({
                    Bucket: 'tms-media' || process.env.S3_BUCKET,
                    Key: decodedObjectKey,
                })
                .promise();

            console.log('downloadAndEncodeImage :: s3Response', s3Response);

            const body = s3Response.Body;

            // Convert S3 Body to base64 properly
            let base64Image;

            if (body instanceof Buffer) {
                // Node.js Buffer (most common case)
                base64Image = body.toString('base64');
            } else if (body instanceof Uint8Array) {
                // Uint8Array
                base64Image = btoa(
                    body.reduce(
                        (acc, byte) => acc + String.fromCharCode(byte),
                        ''
                    )
                );
            } else if (body && body.data) {
                // Buffer-like object with data property
                const uint8Array = new Uint8Array(body.data);
                base64Image = btoa(
                    uint8Array.reduce(
                        (acc, byte) => acc + String.fromCharCode(byte),
                        ''
                    )
                );
            } else {
                throw new Error('Unsupported S3 response body format');
            }

            console.log(
                'downloadAndEncodeImage :: base64Image length:',
                base64Image.length
            );

            // Debug: Check if base64 is valid by trying to decode it
            try {
                const testBytes = this.base64ToUint8Array(base64Image);
                const format = this.validateImageFormat(testBytes);
                console.log(
                    'downloadAndEncodeImage :: detected format:',
                    format
                );
                console.log(
                    'downloadAndEncodeImage :: first 12 bytes:',
                    Array.from(testBytes.slice(0, 12))
                        .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                        .join(' ')
                );

                // Convert WebP to JPEG if needed (AWS Rekognition doesn't support WebP)
                if (format === 'WebP') {
                    console.log(
                        'downloadAndEncodeImage :: Converting WebP to JPEG for AWS Rekognition compatibility'
                    );
                    base64Image = await this.convertWebPToJPEG(base64Image);
                }
            } catch (debugError) {
                console.error(
                    'downloadAndEncodeImage :: debug validation failed:',
                    debugError
                );
            }

            return base64Image;
        } catch (error) {
            console.error(
                'FaceComparisonUtility :: Error in downloadAndEncodeImage:',
                error
            );
            throw error;
        }
    }

    /**
     * Compare two faces using AWS Rekognition
     * @param {File|Blob|string} sourceImage - Source image (File/Blob or URL)
     * @param {File|Blob|string} targetImage - Target image (File/Blob or URL)
     * @param {number} similarityThreshold - Minimum similarity threshold (default: 70)
     * @returns {Promise<Object>} Comparison result
     */
    static async compareFaces(
        sourceImage,
        targetImage,
        similarityThreshold = REKOGNITION_CONFIG.similarityThreshold
    ) {
        try {
            console.log(
                'Face comparison - Source image type:',
                typeof sourceImage,
                sourceImage
            );
            console.log(
                'Face comparison - Target image type:',
                typeof targetImage,
                targetImage
            );

            // Convert images to base64
            let sourceBase64, targetBase64;

            if (typeof sourceImage === 'string') {
                // URL
                console.log('Converting source URL to base64:', sourceImage);
                sourceBase64 = await this.downloadAndEncodeImage({
                    objectKey: sourceImage,
                });
            } else {
                // File/Blob
                console.log('Converting source File/Blob to base64');
                sourceBase64 = await this.fileToBase64(sourceImage);
            }

            if (typeof targetImage === 'string') {
                // URL
                console.log('Converting target URL to base64:', targetImage);
                targetBase64 = await this.downloadAndEncodeImage({
                    objectKey: targetImage,
                });
            } else {
                // File/Blob
                console.log('Converting target File/Blob to base64');
                targetBase64 = await this.fileToBase64(targetImage);
            }

            console.log(
                'Base64 conversion completed, calling AWS Rekognition...'
            );

            // Validate image formats and convert WebP to JPEG if needed
            let sourceFormat, targetFormat;
            let sourceBytes, targetBytes;

            // Process source image
            sourceBytes = this.base64ToUint8Array(sourceBase64);
            sourceFormat = this.validateImageFormat(sourceBytes);

            console.log('Source image format:', sourceFormat);
            console.log('Source image bytes length:', sourceBytes.length);

            // Convert WebP to JPEG if needed (AWS Rekognition doesn't support WebP)
            if (sourceFormat === 'WebP') {
                console.log(
                    'Converting source WebP to JPEG for AWS Rekognition compatibility'
                );
                sourceBase64 = await this.convertWebPToJPEG(sourceBase64);
                sourceBytes = this.base64ToUint8Array(sourceBase64);
                sourceFormat = this.validateImageFormat(sourceBytes);
                console.log('Source image converted to format:', sourceFormat);
                console.log(
                    'Source image new bytes length:',
                    sourceBytes.length
                );
            }

            // Process target image
            targetBytes = this.base64ToUint8Array(targetBase64);
            targetFormat = this.validateImageFormat(targetBytes);

            console.log('Target image format:', targetFormat);
            console.log('Target image bytes length:', targetBytes.length);

            // Convert WebP to JPEG if needed (AWS Rekognition doesn't support WebP)
            if (targetFormat === 'WebP') {
                console.log(
                    'Converting target WebP to JPEG for AWS Rekognition compatibility'
                );
                targetBase64 = await this.convertWebPToJPEG(targetBase64);
                targetBytes = this.base64ToUint8Array(targetBase64);
                targetFormat = this.validateImageFormat(targetBytes);
                console.log('Target image converted to format:', targetFormat);
                console.log(
                    'Target image new bytes length:',
                    targetBytes.length
                );
            }

            // Validate image sizes (AWS Rekognition has limits)
            if (sourceBytes.length === 0 || targetBytes.length === 0) {
                throw new Error('One or both images are empty');
            }

            if (
                sourceBytes.length > 5 * 1024 * 1024 ||
                targetBytes.length > 5 * 1024 * 1024
            ) {
                throw new Error(
                    'Image size too large. AWS Rekognition supports images up to 5MB'
                );
            }

            // Debug: Log first few bytes of each image
            console.log(
                'Source image first 12 bytes:',
                Array.from(sourceBytes.slice(0, 12))
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' ')
            );
            console.log(
                'Target image first 12 bytes:',
                Array.from(targetBytes.slice(0, 12))
                    .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                    .join(' ')
            );

            if (!sourceFormat) {
                throw new Error(
                    'Source image format is not supported or corrupted. First 12 bytes: ' +
                        Array.from(sourceBytes.slice(0, 12))
                            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                            .join(' ')
                );
            }

            if (!targetFormat) {
                throw new Error(
                    'Target image format is not supported or corrupted. First 12 bytes: ' +
                        Array.from(targetBytes.slice(0, 12))
                            .map((b) => '0x' + b.toString(16).padStart(2, '0'))
                            .join(' ')
                );
            }

            // Create the parameters for AWS SDK v2
            // For browser environment, AWS SDK v2 can accept Uint8Array directly
            // But let's try converting to Buffer if available, otherwise use Uint8Array
            let sourceImageBytes, targetImageBytes;

            if (typeof Buffer !== 'undefined') {
                // Node.js environment or browser with Buffer polyfill
                sourceImageBytes = Buffer.from(sourceBytes);
                targetImageBytes = Buffer.from(targetBytes);
                console.log('Using Buffer for AWS SDK');
            } else {
                // Pure browser environment
                sourceImageBytes = sourceBytes;
                targetImageBytes = targetBytes;
                console.log('Using Uint8Array for AWS SDK');
            }

            const params = {
                SourceImage: { Bytes: sourceImageBytes },
                TargetImage: { Bytes: targetImageBytes },
                SimilarityThreshold: similarityThreshold,
            };

            console.log('AWS Rekognition params:', {
                sourceImageSize: sourceImageBytes.length,
                targetImageSize: targetImageBytes.length,
                similarityThreshold: similarityThreshold,
                sourceImageType: sourceImageBytes.constructor.name,
                targetImageType: targetImageBytes.constructor.name,
                sourceIsBuffer:
                    typeof Buffer !== 'undefined'
                        ? Buffer.isBuffer(sourceImageBytes)
                        : false,
                targetIsBuffer:
                    typeof Buffer !== 'undefined'
                        ? Buffer.isBuffer(targetImageBytes)
                        : false,
            });

            // Execute the API call using AWS SDK v2
            console.log('Calling AWS Rekognition compareFaces...');
            console.log(
                'Full params object:',
                JSON.stringify(
                    {
                        ...params,
                        SourceImage: {
                            Bytes: `[${sourceImageBytes.constructor.name} of length ${sourceImageBytes.length}]`,
                        },
                        TargetImage: {
                            Bytes: `[${targetImageBytes.constructor.name} of length ${targetImageBytes.length}]`,
                        },
                    },
                    null,
                    2
                )
            );

            const response = await rekognition.compareFaces(params).promise();

            return {
                success: true,
                data: response,
                hasMatch:
                    response.FaceMatches && response.FaceMatches.length > 0,
                similarity:
                    response.FaceMatches && response.FaceMatches.length > 0
                        ? response.FaceMatches[0].Similarity
                        : 0,
                matchCount: response.FaceMatches
                    ? response.FaceMatches.length
                    : 0,
            };
        } catch (error) {
            console.error('Face comparison error:', error);
            return {
                success: false,
                error: error.message,
                hasMatch: false,
                similarity: 0,
                matchCount: 0,
            };
        }
    }

    /**
     * Validate face comparison result
     * @param {Object} result - Result from compareFaces
     * @param {number} minSimilarity - Minimum required similarity (default: 70)
     * @returns {Object} Validation result
     */
    static validateFaceComparison(
        result,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        if (!result.success) {
            return {
                isValid: false,
                message: `Face comparison failed: ${result.error}`,
                similarity: 0,
            };
        }

        if (!result.hasMatch) {
            return {
                isValid: false,
                message: 'No matching faces found between the images',
                similarity: 0,
            };
        }

        if (result.similarity < minSimilarity) {
            return {
                isValid: false,
                message: `Face similarity (${result.similarity.toFixed(1)}%) is below required threshold (${minSimilarity}%)`,
                similarity: result.similarity,
            };
        }

        return {
            isValid: true,
            message: `Face verification successful (${result.similarity.toFixed(1)}% similarity)`,
            similarity: result.similarity,
        };
    }

    /**
     * Compare camera field images with profile picture
     * @param {Object} cameraFiles - Camera files object with sections
     * @param {Array} verificationFields - Array of field keys to verify
     * @param {string} profilePictureUrl - Profile picture URL
     * @param {number} minSimilarity - Minimum required similarity
     * @returns {Promise<Object>} Verification result
     */
    static async verifyCameraFieldsWithProfile(
        cameraFiles,
        verificationFields,
        profilePictureUrl,
        minSimilarity = REKOGNITION_CONFIG.similarityThreshold
    ) {
        console.log('verifyCameraFieldsWithProfile', cameraFiles);
        console.log('verifyCameraFieldsWithProfile', verificationFields);
        console.log('verifyCameraFieldsWithProfile', profilePictureUrl);
        console.log('verifyCameraFieldsWithProfile', minSimilarity);
        if (!profilePictureUrl) {
            return {
                success: false,
                message:
                    'Profile picture not found. Please upload a profile picture first.',
                results: [],
            };
        }

        if (!verificationFields || verificationFields.length === 0) {
            return {
                success: true,
                message: 'No verification fields specified',
                results: [],
            };
        }

        const results = [];
        let allValid = true;
        let errorMessages = [];

        for (const fieldKey of verificationFields) {
            const fieldFiles = cameraFiles[fieldKey];

            if (!fieldFiles || fieldFiles.length === 0) {
                allValid = false;
                errorMessages.push(`No image found for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `No image found for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            // Use the first image from the field
            const firstFile = fieldFiles[0];
            let imageToCompare;

            if (typeof firstFile === 'string') {
                // Camera files are stored as S3 paths (strings)
                imageToCompare = firstFile;
            } else if (firstFile && firstFile.url) {
                // Use the URL if available (object format)
                imageToCompare = firstFile.url;
            } else if (firstFile && firstFile.file) {
                // Use the file object
                imageToCompare = firstFile.file;
            } else if (firstFile) {
                // Fallback to the file itself
                imageToCompare = firstFile;
            } else {
                allValid = false;
                errorMessages.push(`Invalid image data for field: ${fieldKey}`);
                results.push({
                    fieldKey,
                    success: false,
                    message: `Invalid image data for field: ${fieldKey}`,
                    similarity: 0,
                });
                continue;
            }

            try {
                const comparisonResult = await this.compareFaces(
                    imageToCompare,
                    profilePictureUrl,
                    minSimilarity
                );
                const validation = this.validateFaceComparison(
                    comparisonResult,
                    minSimilarity
                );

                results.push({
                    fieldKey,
                    success: validation.isValid,
                    message: validation.message,
                    similarity: validation.similarity,
                    comparisonData: comparisonResult.data,
                });

                if (!validation.isValid) {
                    allValid = false;
                    errorMessages.push(`${fieldKey}: ${validation.message}`);
                }
            } catch (error) {
                allValid = false;
                const errorMsg = `Face comparison failed for ${fieldKey}: ${error.message}`;
                errorMessages.push(errorMsg);
                results.push({
                    fieldKey,
                    success: false,
                    message: errorMsg,
                    similarity: 0,
                });
            }
        }

        return {
            success: allValid,
            message: allValid
                ? 'All face verifications passed successfully'
                : `Face verification failed: ${errorMessages.join('; ')}`,
            results,
            errorMessages,
        };
    }
}

export default FaceComparisonUtility;
