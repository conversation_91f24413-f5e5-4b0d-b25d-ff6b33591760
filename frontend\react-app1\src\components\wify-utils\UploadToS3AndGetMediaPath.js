import { getPresignedUrl } from '../wify-utils/S3Uploader/S3Uploader';
import axios from 'axios';
/*
    This will accept a file to be uploaded
    Input -->
    authToken={http_utils.getAuthToken()}
    prefixDomain = {http_utils.getCDNDomain()}
    file name
    file_url
    Output <-----
    uploaded_file_url -> This can be give to a s3 uploader as initial files, then it can show the preview
*/
export const UploadToS3AndGetMediaPath = async (
    blob_url,
    file_name,
    progressCallback
) => {
    console.log('UploadToS3AndGetMediaPath called', blob_url, file_name);
    let resp = await getPresignedUrl(file_name);
    // console.log('Rxd Resp in dropzone get upload params',resp);
    let uploadUrl = resp.url;
    let fields = resp.fields;
    let fileUrl = encodeURI(resp.fields.key);
    let formData = new FormData();
    Object.keys(fields).forEach((singleKey) => {
        formData.append(singleKey, fields[singleKey]);
    });

    const config = {
        responseType: 'blob',
        onUploadProgress: function (progressEvent) {
            // console.log(progressEvent.loaded);
            let percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
            );
            if (progressCallback) {
                progressCallback(percentCompleted, fileUrl, false);
            }
        },
    };

    // Fetch the file from the blob URL
    const response = await axios.get(blob_url, config);
    formData.append('file', new File([response.data], file_name));
    console.log('UploadToS3AndGetMediaPath formData', JSON.stringify(formData));
    for (var pair of formData.entries()) {
        console.log(pair[0] + ', ' + pair[1]);
    }
    // Upload the file to S3
    const uploadResp = await axios.post(uploadUrl, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: config.onUploadProgress, // Keep the progress tracking here
    });

    // Check if the upload was successful
    if (uploadResp.status === 204 || uploadResp.status === 200) {
        // Success, upload is complete
        if (progressCallback) {
            progressCallback(100, fileUrl, true); // send true when the upload finished by server
        }
        return fileUrl;
    } else {
        throw new Error('File upload failed');
    }
};
