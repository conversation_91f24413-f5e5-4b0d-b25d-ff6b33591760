import React, { Component } from 'react';
import {
    Modal,
    Form,
    Radio,
    Input,
    Button,
    Popover,
    Steps,
    message,
    Row,
    Col,
    Avatar,
    Tooltip,
    Checkbox,
    Typography,
    Upload,
    Timeline,
    List,
    Collapse,
    Spin,
    Alert,
} from 'antd';
import FormBuilder from 'antd-form-builder';
import http_utils from '../../util/http_utils';
import CircularProgress from '../../components/CircularProgress';
import {
    AntDesignOutlined,
    EllipsisOutlined,
    InboxOutlined,
    RightOutlined,
    ShareAltOutlined,
    UndoOutlined,
    UploadOutlined,
    UserOutlined,
} from '@ant-design/icons';
// import ReactTags from 'react-tag-autocomplete'
import <PERSON>agger from 'antd/lib/upload/Dragger';
import RemoteSourceSelect from '../../components/wify-utils/RemoteSourceSelect';
import {
    convertDateFieldsToMoments,
    convertUTCToDisplayTime,
    durationAsString,
    isTimePassed,
    priorities,
    getGeneralFileSection,
    getTouchedFieldsValueInForm,
    hasAnyFileChanged,
    isAndroidApp,
    generateUUID,
    setRemarkFieldAsNoRemarkIfEmpty,
    showCreateOrUpdateSuccessMessage,
} from '../../util/helpers';
import TaskSummaryView from '../../components/WIFY/subtasks/TaskSummaryView';
import TaskTime from '../../components/WIFY/subtasks/TaskTime';
import TimePickerWidget from '../../components/wify-utils/TimePickerWidget';
import moment from 'moment';
import S3Uploader from '../../components/wify-utils/S3Uploader/S3Uploader';
import {
    decodeFieldsMetaFrmJson,
    decodeFileSectionsFrmJson,
    decodeMicSectionsFrmJson,
    decodeCameraSectionsFrmJson,
    decodeBLESectionsFrmJson,
} from '../../components/wify-utils/FieldCreator/helpers';
import { getCustomFieldsJsonFrStatus } from './SingleStatusUpdates';
import { isArray } from 'lodash';
import LocationDetails from './LocationDetails';
import { defaultLocationVerificationStatus } from '../../components/WIFY/helpers';
import MicInputV2 from '../../components/wify-utils/MicInput_v2';
import CameraInput from '../../components/wify-utils/CameraInput';
import Countdown from 'antd/lib/statistic/Countdown';
import SubtaskCardConfigHelper from '../../components/WIFY/subtasks/SubtaskCardConfigHelper';
import _ from 'lodash';
import { DraftManager } from './DraftManager';
import MyTaskConfigHelper from './MyTaskConfigHelper';
import PartsEditor from './PartsEditor';
import checkFeatureAccess from '../../util/FeatureAccess';
import FaceComparisonUtility from '../../components/wify-utils/FaceComparisonUtility';
import { getProfilePictureUrl } from '../../util/imageUtils';

// import TimelineCard from './TimelineCard';
// import {Paragraph} from 'antd';

const protoUrl = '/my-tasks/proto';
const submitUrl = '/my-tasks';
// const otpVerityUrl = "/my-tasks/otp-verify";
const reSendOtpUrl = '/my-tasks/re-send-otp';
const dynamicFormLogicPath = '/my-tasks/exec-dynamic-form-logic';
let debouncer;

function disabledDate(current) {
    // Can not select days before today and today
    return current && current < moment().subtract(1, 'days').endOf('day');
}

class TaskUpdateEditor extends Component {
    constructor(props) {
        super(props);
        this.formRef = React.createRef();
        this.cameraRef = React.createRef();
        // console.log('Rxd props task update editor',props);
    }
    initState = {
        render_helper: false,
        visible: false,
        isFormSubmitting: false,
        viewData: undefined,
        isLoadingViewData: false,
        editMode: this.props.editMode,
        error: '',
        currentStep: 0,
        // Attachements and custom files section
        fileSections: [],
        filesBySection: {},
        sectionWiseUploaderReady: {},
        // Mic sections
        micSections: [],
        micRecordingsBySection: {},
        sectionWiseMicUploaderReady: {},
        //Camera section
        cameraSections: [],
        cameraRecordingsBySection: {},
        sectionWiseCameraUploaderReady: {},
        // ---
        isLoadingLocation: false,
        locationData: undefined,
        //otp section
        otpRegenrated: false,
        // dynamic form
        manipulatedDynamicMeta: undefined,
        isExecutingDynamicFormLogic: false,
        disableSubmissionButton: false,
        updateClosureToChildComponent: true,
        isDynamicFormFirstExec: true,
        uploadedFilesBySection: {},
        partsData: [],
        draftRetryCount: 0, // Track retry attempts for draft application
        isDynamicFormFailed: false,
        // Face verification status
        faceVerificationStatus: null, // null, 'success', 'failed'
        faceVerificationMessage: '',
    };

    state = this.initState;

    componentDidMount() {
        this.initViewData();
        this.verifyFeatureAccess();
    }

    verifyFeatureAccess = async () => {
        try {
            let hasAccess = await checkFeatureAccess('TMS250523533568');
            this.setState({ TMS250523533568: hasAccess });
        } catch (error) {
            this.setState({ TMS250523533568: false });
            console.error(
                'TaskUpdateEditor :: verifyFeatureAccess :: error : ',
                error
            );
        }

        // Check access for draft functionality feature flag
        try {
            let hasDraftAccess = await checkFeatureAccess('TMS250617437781');
            this.setState({ TMS250617437781: hasDraftAccess });
        } catch (error) {
            this.setState({ TMS250617437781: false });
            console.error(
                'TaskUpdateEditor :: verifyFeatureAccess :: TMS250617437781 :: error : ',
                error
            );
        }

        // Check access for OTP closure code feature flag
        try {
            let hasStaticOtpAccess =
                await checkFeatureAccess('TMS251008278472');
            this.setState({ TMS251008278472: hasStaticOtpAccess });
        } catch (error) {
            this.setState({ TMS251008278472: false });
            console.error(
                'TaskUpdateEditor :: verifyFeatureAccess :: TMS251008278472 :: error : ',
                error
            );
        }
    };

    initGeoFenceStuff() {
        // console.log('CDM');
        // console.log('props',this.props);
        // console.log('config_data',this.props.updateTypeDetails?.config_data);

        // message.success(
        //     isAndroidApp() ?
        //     'Is android app' :
        //     'Not android app'
        // )

        // console.log('isArray ',isArray(geo_verification_enabled_statuses))
        // console.log('update_status_key',update_status_key);
        if (this.needToCaptureLocation()) {
            this.setState({
                isLoadingLocation: true,
            });
            this.getGPSLocationFrmDevice();
            // console.log('Check if Geo verification is enabled for the status & whether this is android App passed');
        }
    }

    needToCaptureLocation() {
        const update_status_key = this.props.updateTypeId;
        const geo_verification_enabled_statuses =
            this.props.updateTypeDetails?.config_data
                ?.enable_geo_verification_for;
        return (
            isArray(geo_verification_enabled_statuses) &&
            geo_verification_enabled_statuses.includes(update_status_key)
        );
        // TODO uncomment before push && isAndroidApp()
    }

    /**
     * Check if selfie verification is enabled for the current status
     */
    isSelfieVerificationEnabled() {
        const config_data = this.props.updateTypeDetails?.config_data;
        const update_status_key = this.props.updateTypeId;

        // Check if selfie verification is enabled globally
        if (!config_data?.enable_selfie_verification) {
            return false;
        }

        // Check if current status is in the enabled statuses list
        const selfie_verification_statuses =
            config_data?.selfie_verification_statuses;
        if (!isArray(selfie_verification_statuses)) {
            return false;
        }

        return selfie_verification_statuses.includes(update_status_key);
    }

    /**
     * Get the camera fields that need selfie verification for the current status
     */
    getSelfieVerificationFields() {
        const config_data = this.props.updateTypeDetails?.config_data;

        if (!this.isSelfieVerificationEnabled()) {
            return [];
        }

        const selfie_verification_fields =
            config_data?.selfie_verification_fields;
        if (!isArray(selfie_verification_fields)) {
            return [];
        }
        const selfieVerificationFieldsOfCurrentStatus = [];
        selfie_verification_fields.forEach((field) => {
            const statusKey = field.split('_')[0];
            const fieldKey = field.split('_')[1];
            if (statusKey == this.props.updateTypeId) {
                selfieVerificationFieldsOfCurrentStatus.push(fieldKey);
            }
        });
        return selfieVerificationFieldsOfCurrentStatus;
    }

    /**
     * Get profile picture URL from localStorage
     */
    getProfilePictureUrl() {
        try {
            const user = JSON.parse(localStorage.getItem('user'));
            if (user?.profile_picture_url) {
                // Use the same helper as other parts of the application
                return user.profile_picture_url;
            }
        } catch (error) {
            console.error('Error getting profile picture URL:', error);
        }
        return null;
    }

    getGPSLocationFrmDevice() {
        return new Promise((resolve, reject) => {
            console.log('gpsLocation', 'Getting GPS location..');
            if (window.wifyApp?.getGPSLocation) {
                const locationRequestEventName =
                    'location_request_' + generateUUID();
                window.addEventListener(locationRequestEventName, (e) => {
                    const locationData = e.detail;
                    setTimeout(() => {
                        this.recivedLocationData(locationData);
                    }, 1000);

                    // console.log('Received gpsLocation from android -',JSON.stringify(e.detail));
                });
                const appCallResp = window.wifyApp?.getGPSLocation(
                    locationRequestEventName
                );
                resolve();
            } else {
                // const dummyLocationData = {"latitude":19.2181636,"longitude":72.8596517,"isMock":false,"timestamp":1653559234,"speed":0,"bearing":0,"altitude":-47,"accuracy":49.86800003051758,"address":{"text":"RAJNANDHAN CHS A-D, SAMVED CO-OPERATIVE HOUSING SOCIETY SAMVEL CHS, राजेंद्र नगर, बोरिवली ईस्ट, मुंबई, महाराष्ट्र 400066, India","area":"महाराष्ट्र","locality":"मुंबई","postalCode":"400066"},"device_info":{"device_unique_id":"0e7ca81e10524a85","device_android_version":"Android v10.0, API Level: 29","device_mode_name":"HMD Global Nokia 6.1","battery":96,"battery_acc":false}}
                // console.log('Loaded dummy location data');
                message.success('Please update your app');
                // setTimeout(()=>{
                //     this.recivedLocationData(dummyLocationData);
                // },1000)
                resolve();
            }
        });
    }

    recivedLocationData(locationData) {
        this.setState({
            isLoadingLocation: false,
            locationData: locationData,
        });
    }

    initViewData() {
        // console.log("Trying to init view Data",this.state.visible);
        this.initGeoFenceStuff();
        if (
            (this.state.editMode && this.state.visible) ||
            (!this.state.editMode &&
                this.state.viewData == undefined &&
                !this.state.isLoadingViewData)
        ) {
            this.setState({
                isLoadingViewData: true,
            });
            var params = {};
            params['sbtsk_type_id'] =
                '' + this.props.editorItem.sbtsk_type.value;
            params['srvcReqId'] = this.props.editorItem.srvc_req_details?.id;
            const onComplete = (resp) => {
                this.initConfigData(
                    resp,
                    // then set loading false
                    this.setState(
                        {
                            isLoadingViewData: false,
                            viewData: resp.data,
                            error: '',
                        },
                        () => {
                            if (this.isDynamicForm()) {
                                this.onFormValueChanged({}, {}); // CDM
                            }
                        }
                    )
                );
            };
            const onError = (error) => {
                // console.log(error.response.status);
                this.setState({
                    isLoadingViewData: false,
                    error: http_utils.decodeErrorToMessage(error),
                });
            };
            var url =
                protoUrl +
                '/' +
                this.props.editorItem.id +
                '/' +
                this.props.updateTypeId;
            // console.log(url);
            http_utils.performGetCall(url, params, onComplete, onError);
        }
    }

    initConfigData(entry_specific_data, then, dont_override_files = false) {
        let isGeneralFileSectionMandatory =
            SubtaskCardConfigHelper.isDefaultAttachmentFieldMandatory(
                this.props.updateTypeDetails?.config_data,
                this.props.updateTypeId
            );
        let newFileSections = [
            getGeneralFileSection(isGeneralFileSectionMandatory),
        ];
        let custom_file_sections = this.getCustomFileSectionsFrmConfig();
        if (custom_file_sections && custom_file_sections.length > 0) {
            const showAttachMentsAtBottom =
                this.props.updateTypeDetails?.config_data?.[
                    `move_attachments_field_to_bottom_for_${this.props.updateTypeId}`
                ];
            newFileSections = showAttachMentsAtBottom
                ? [...custom_file_sections, ...newFileSections]
                : [...newFileSections, ...custom_file_sections];
        }
        // console.log('newFileSections',newFileSections)

        // Prefilling attachments
        let initialFilesBySection = dont_override_files
            ? this.state.filesBySection
            : {};

        if (entry_specific_data.data.form_data?.form_data) {
            initialFilesBySection =
                entry_specific_data.data.form_data?.form_data['attachments'];
        }

        let newMicSections = [];
        let customMicSections = this.getCustomMicSectionsFrmConfig();
        if (customMicSections && customMicSections.length > 0) {
            newMicSections = [...newMicSections, ...customMicSections];
        }

        let initialMicRecordingsBySection = dont_override_files
            ? this.state.micRecordingsBySection
            : {};

        if (entry_specific_data.data.form_data?.form_data) {
            initialMicRecordingsBySection =
                entry_specific_data.data.form_data?.form_data['mic_files'];
        }

        let newCameraSections = [];
        let customCameraSections = this.getCustomCameraSectionsFrmConfig();
        if (customCameraSections && customCameraSections.length > 0) {
            newCameraSections = [...newCameraSections, ...customCameraSections];
        }

        let initialCameraRecordingsBySection = dont_override_files
            ? this.state.cameraRecordingsBySection
            : {};
        if (entry_specific_data.data.form_data?.form_data) {
            initialCameraRecordingsBySection =
                entry_specific_data.data.form_data?.form_data['camera_files'];
        }

        this.setState(
            {
                fileSections: newFileSections,
                filesBySection: { ...initialFilesBySection },
                micSections: newMicSections,
                micRecordingsBySection: { ...initialMicRecordingsBySection },
                cameraSections: newCameraSections,
                cameraRecordingsBySection: {
                    ...initialCameraRecordingsBySection,
                },
            },
            then
        );
    }

    getCustomMicSectionsFrmConfig() {
        return decodeMicSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());
    }

    getCustomCameraSectionsFrmConfig() {
        return decodeCameraSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());
    }

    getCustomFileSectionsFrmConfig() {
        return decodeFileSectionsFrmJson(this.getCustomFieldsJsonFrmConfig());
    }

    componentDidUpdate(prevProps, prevState) {
        if (
            prevProps.editorItem != this.props.editorItem ||
            prevProps.showEditor != this.props.showEditor
        ) {
            this.setState(
                {
                    render_helper: !this.state.render_helper,
                    visible: this.props.showEditor,
                    draftRetryCount: 0, // Reset retry counter when editor opens
                },
                function () {
                    if (this.props.showEditor && this.state.editMode) {
                        this.initViewData();
                    }
                }
            );
        } else {
            if (this.state.refreshOnUpdate) {
                this.setState(
                    {
                        refreshOnUpdate: false,
                    },
                    this.initViewData()
                );
            }
        }
    }

    handleOk = () => {
        this.setState({
            visible: false,
            isFormSubmitting: false,
        });
        this.updateClosureToParent();
    };

    updateClosureToParent() {
        if (this.props.onClose != undefined) {
            this.props.onClose();
        }
        this.setState({
            refreshOnUpdate: true,
            ...this.initState,
        });
    }
    resetReadyStatusOfCamera() {
        Object.keys(this.state.sectionWiseCameraUploaderReady).forEach(
            (singleKey) =>
                (this.state.sectionWiseCameraUploaderReady[singleKey] = true)
        );
    }

    tellParentToRefreshList(entry_id) {
        // console.log("Trying to to tell parent to refresh list");
        if (this.props.onDataModified != undefined) {
            this.props.onDataModified(entry_id);
        }
    }

    showDraftSavedPopUp() {
        message.info(
            {
                style: {
                    marginTop: '50vh',
                },
                content: 'Saved as draft',
            },
            3000
        );
    }

    handleCancel = () => {
        this.setState(
            {
                updateClosureToChildComponent: false,
            },
            () => {
                this.setState({
                    visible: false,
                });
                this.resetReadyStatusOfCamera();
                if (this.cameraRef?.current?.stopCapturing) {
                    this.cameraRef.current.stopCapturing();
                }
                this.updateClosureToParent();
                this.setState({
                    uploadedFilesBySection: {},
                });
            }
        );
    };

    mandatoryStatusRequirement(reqStatusArray) {
        let statusToBeCompleted = [];
        this.props.updateTypeDetails.statuses.forEach((singleStatus) => {
            if (reqStatusArray.includes(singleStatus.value)) {
                statusToBeCompleted.push(singleStatus.title);
            }
        });
        return statusToBeCompleted.join(', ');
    }

    hasAnyBarcodeValueChanged = (form_data, fieldMeta, prefill_data) => {
        let returnFields = {};

        fieldMeta.forEach((singleMeta) => {
            if (
                form_data?.[singleMeta.key] != prefill_data?.[singleMeta.key] &&
                singleMeta?.type == 'Barcode_scanner'
            ) {
                returnFields[singleMeta.key] = form_data[singleMeta.key];
            }
        });
        return returnFields;
    };

    getOriginalMetaFrDynamicForm() {
        let custFieldJson = getCustomFieldsJsonFrStatus(
            this.props.updateTypeId,
            this.props.updateTypeDetails?.config_data
        );
        if (custFieldJson) {
            custFieldJson = JSON.parse(custFieldJson)?.translatedFields;
            // custFieldJson = decodeFieldsMetaFrmJson(custFieldJson,undefined,true,true,this.formRef);
        }
        return custFieldJson;
    }

    setIsExecutingDynamicFormLogic(value) {
        this.setState({
            isExecutingDynamicFormLogic: value,
        });
    }

    // Helper method to check which draft fields are missing from current form values
    getMissingDraftFields(draftData, currentFormValues) {
        if (!draftData || !currentFormValues) return {};

        const missingFields = {};
        Object.keys(draftData).forEach((key) => {
            // Skip file-related fields as they are handled separately
            if (['attachments', 'mic_files', 'camera_files'].includes(key)) {
                return;
            }

            const draftValue = draftData[key];
            const currentValue = currentFormValues[key];

            // Check if the field is missing or has different value
            if (draftValue && !currentValue) {
                missingFields[key] = draftValue;
            }
        });

        return missingFields;
    }

    // Apply draft data and retry dynamic form logic if needed
    applyDraftAndRetryIfNeeded(draftData, isRetryForDraft = false) {
        const MAX_RETRY_ATTEMPTS = 5; // Prevent infinite loops
        const currentFormValues = this.formRef.current.getFieldsValue();
        console.log('currentFormValues', currentFormValues);
        const missingFields = this.getMissingDraftFields(
            draftData,
            currentFormValues
        );
        console.log('missingFields', missingFields);

        if (
            Object.keys(missingFields).length > 0 &&
            this.state.draftRetryCount < MAX_RETRY_ATTEMPTS
        ) {
            // Apply missing draft fields to the form
            this.formRef.current.setFieldsValue(missingFields);

            // Get updated form values after applying missing fields
            const updatedFormValues = this.formRef.current.getFieldsValue();

            console.log('updatedFormValues', updatedFormValues);

            // Check if we still have missing fields after this application
            const stillMissingFields = this.getMissingDraftFields(
                draftData,
                updatedFormValues
            );

            console.log('stillMissingFields', stillMissingFields);

            if (Object.keys(stillMissingFields).length > 0) {
                // We made progress, retry the dynamic form logic
                console.log(
                    'Retrying dynamic form logic to apply remaining draft fields:',
                    stillMissingFields,
                    'Attempt:',
                    this.state.draftRetryCount + 1
                );
                this.setState({
                    draftRetryCount: this.state.draftRetryCount + 1,
                });
                setTimeout(() => {
                    this.executeDynamicFormLogic({}, updatedFormValues, true);
                }, 100);
            } else {
                // No more progress can be made or all fields applied
                this.finalizeDraftApplication();
            }
        } else {
            this.runFinalDynamicLogicExecution();
            // All draft fields are applied or max retries reached
            this.finalizeDraftApplication();
        }
    }

    // Run final dynamic logic execution to ensure all lambda logic is applied with current form data
    runFinalDynamicLogicExecution() {
        console.log(
            'Running final dynamic logic execution to ensure all lambda logic is applied'
        );
        const currentFormValues = this.formRef.current.getFieldsValue();

        // Execute dynamic form logic one final time with current form values
        setTimeout(() => {
            this.executeDynamicFormLogic({}, currentFormValues, false, true); // Added finalExecution flag
        }, 100);
    }

    // Finalize draft application and reset state
    finalizeDraftApplication() {
        this.setIsExecutingDynamicFormLogic(false);
        this.setState({
            isDynamicFormFirstExec: false,
            draftRetryCount: 0, // Reset retry counter
        });
    }

    executeDynamicFormLogic = (
        changedValues,
        allValues,
        isRetryForDraft = false
    ) => {
        // Write a API call to TMS here
        this.setIsExecutingDynamicFormLogic(true);
        //Todo for dynamic form. Key sent so form can see if draft needs to be rendered
        let renderingFrmDraft = false;
        let draftData = null;
        if (this.state.isDynamicFormFirstExec || isRetryForDraft) {
            // Check if there's draft data available for this dynamic form
            draftData = DraftManager.applyDraft(this.draftParams(), true);
            if (draftData && Object.keys(draftData).length > 0) {
                renderingFrmDraft = true;
            }
        }
        // message.warning(<Spin/>);// To be set by jainish

        allValues['attachments'] = this.state.filesBySection;
        allValues['mic_files'] = this.state.micRecordingsBySection;
        allValues['camera_files'] = this.state.cameraRecordingsBySection;

        const params = {
            changedValues,
            allValues,
            meta: this.getOriginalMetaFrDynamicForm(),
            currentMeta: this.state.manipulatedDynamicMeta,
        };
        // console.log('executeDynamicFormLogic params',params);
        params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;
        params['srvc_req_id'] = this.state?.viewData?.form_data?.srvc_req_id;
        params['srvc_type_id'] = this.state?.viewData?.form_data?.srvc_type_id;
        params['renderingFrmDraft'] = renderingFrmDraft;

        http_utils.performPostCall(
            dynamicFormLogicPath +
                '/' +
                this.props.editorItem.id +
                '/' +
                this.props.updateTypeId,
            params,
            (resp) => {
                // console.log('executeDynamicFormLogic resp.data', resp.data);
                const {
                    meta,
                    allValues,
                    changedValues,
                    manipulatedFieldValues,
                    disableFormSubmissionButton,
                    errorMessage,
                } = resp.data.data;
                if (errorMessage) {
                    message.config({
                        duration: errorMessage.duration || 3,
                    });
                    message.error(errorMessage.message);
                }
                this.setState(
                    {
                        isDynamicFormFailed: false,
                        manipulatedDynamicMeta: meta,
                        disableSubmissionButton: disableFormSubmissionButton,
                    },
                    () => {
                        // For files,mic,camera
                        this.initConfigData(
                            {
                                data: this.state.viewData,
                            },
                            () => {
                                this.formRef.current.setFieldsValue(
                                    manipulatedFieldValues
                                );

                                // Apply draft data and retry if needed for dynamic forms
                                if (renderingFrmDraft && draftData) {
                                    this.applyDraftAndRetryIfNeeded(
                                        draftData,
                                        isRetryForDraft
                                    );
                                } else {
                                    this.setIsExecutingDynamicFormLogic(false);
                                    if (
                                        !this.state.isDynamicFormFirstExec &&
                                        !isRetryForDraft
                                    ) {
                                        DraftManager.updateDraft(
                                            this.draftParams()
                                        );
                                    } else {
                                        this.setState({
                                            isDynamicFormFirstExec: false,
                                        });
                                    }
                                }
                            },
                            true
                        );
                    }
                );
            },
            (error) => {
                console.log(
                    'Error in onFormValueChange API call',
                    http_utils.decodeErrorToMessage(error)
                );
                message.error({
                    content:
                        'Failed to load form. Close and reopen the form to reload.',
                    duration: 5,
                });
                this.setIsExecutingDynamicFormLogic(false);
                this.setState({ isDynamicFormFailed: true });
            }
        );
    };

    createParamsFrBleExec() {
        return {
            sbtsk_type_id: this.props.editorItem?.sbtsk_type?.value,
            srvc_req_id: this.state?.viewData?.form_data?.srvc_req_id,
            srvc_type_id: this.state?.viewData?.form_data?.srvc_type_id,
            entry_id: this.props.editorItem?.id,
            update_type_id: this.props?.updateTypeId,
            collab_order_id: this.state?.viewData?.form_data?.collab_order_id,
        };
    }

    bleComponentProps() {
        return {
            taskUpdateVisible: this.state.updateClosureToChildComponent,
            paramsFrLambdaExec: this.createParamsFrBleExec(),
        };
    }

    onFormValueChanged = (changedValues, allValues) => {
        if (debouncer) {
            clearTimeout(debouncer);
        }
        debouncer = setTimeout(() => {
            this.executeDynamicFormLogic(changedValues, allValues);
        }, 750);
    };
    onCustomButtonClick = (fieldId) => {
        if (this.isDynamicForm()) {
            this.onFormValueChanged(
                { [fieldId]: true },
                this.formRef?.current?.getFieldsValue()
            );
        } else {
            console.log('Not a dynamic form');
        }
    };

    stripHiddenUploads(params, manipulatedFormMeta) {
        // Collect keys where hide === true
        const hiddenKeys = new Set(
            manipulatedFormMeta.filter((f) => f.hide === true).map((f) => f.key)
        );

        const buckets = ['attachments', 'mic_files', 'camera_files'];
        const out = { ...params };

        for (const singleBucket of buckets) {
            const obj = out[singleBucket];
            if (obj && typeof obj === 'object' && !Array.isArray(obj)) {
                out[singleBucket] = Object.fromEntries(
                    Object.entries(obj).filter(([key]) => !hiddenKeys.has(key))
                );
            }
        }

        return out;
    }

    /**
     * Perform face verification if enabled
     */
    async performFaceVerification() {
        if (!this.isSelfieVerificationEnabled()) {
            return { success: true, message: 'Face verification not required' };
        }

        const verificationFields = this.getSelfieVerificationFields();
        if (verificationFields.length === 0) {
            return {
                success: true,
                message: 'No verification fields configured',
            };
        }

        const profilePictureUrl = this.getProfilePictureUrl();
        if (!profilePictureUrl) {
            return {
                success: false,
                message:
                    'Profile picture not found. Please upload a profile picture in your account settings first.',
            };
        }

        try {
            const result =
                await FaceComparisonUtility.verifyCameraFieldsWithProfile(
                    this.state.cameraRecordingsBySection,
                    verificationFields,
                    profilePictureUrl,
                    70 // similarity threshold
                );

            return result;
        } catch (error) {
            console.error('Face verification error:', error);
            return {
                success: false,
                message: `Face verification failed: ${error.message}`,
            };
        }
    }

    submitForm = async (data) => {
        this.setState({
            isFormSubmitting: true,
        });

        // Check face verification status if enabled
        if (this.isSelfieVerificationEnabled()) {
            if (this.state.faceVerificationStatus === 'failed') {
                this.setState({
                    isFormSubmitting: false,
                    error:
                        this.state.faceVerificationMessage ||
                        'Face verification failed',
                });
                message.error(
                    'Please complete face verification before submitting'
                );
                return;
            } else if (this.state.faceVerificationStatus === 'loading') {
                this.setState({
                    isFormSubmitting: false,
                    error: 'Face verification is in progress',
                });
                message.warning(
                    'Please wait for face verification to complete'
                );
                return;
            } else if (this.state.faceVerificationStatus !== 'success') {
                this.setState({
                    isFormSubmitting: false,
                    error: 'Face verification required',
                });
                message.error('Please take a photo for face verification');
                return;
            }
        }

        let touchedFields = getTouchedFieldsValueInForm(
            data,
            this.formRef?.current,
            this.getSpecificFieldsFrStatus().fields
        );
        touchedFields = {
            ...touchedFields,
            ...this.hasAnyBarcodeValueChanged(
                data,
                this.getSpecificFieldsFrStatus().fields,
                this.state.viewData?.form_data?.form_data
            ),
        };
        if (this.state.editMode) {
            // compare files with prefill data
            let isAnyFileChanged = hasAnyFileChanged(
                this.state.filesBySection,
                this.state.viewData?.form_data?.form_data?.attachments
            );
            if (isAnyFileChanged) {
                touchedFields['attachments'] = this.state.filesBySection;
            }

            // compare files with prefill data
            let isAnyMicFileChanged = hasAnyFileChanged(
                this.state.micRecordingsBySection,
                this.state.viewData?.form_data?.form_data?.mic_files
            );
            if (isAnyMicFileChanged) {
                touchedFields['mic_files'] = this.state.micRecordingsBySection;
            }

            // compare camera files with prefill data
            let isAnyCameraFileChanged = hasAnyFileChanged(
                this.state.cameraRecordingsBySection,
                this.state.viewData?.form_data?.form_data?.camera_files
            );
            if (isAnyCameraFileChanged) {
                touchedFields['camera_files'] =
                    this.state.cameraRecordingsBySection;
            }
            touchedFields['sbtsk_parts_consumption'] = this.state.partsData;
        } else {
            data['attachments'] = this.state.filesBySection;
            data['mic_files'] = this.state.micRecordingsBySection;
            data['camera_files'] = this.state.cameraRecordingsBySection;
            data['sbtsk_parts_consumption'] = this.state.partsData;
        }
        let draftData = DraftManager.applyDraft(
            this.draftParams(),
            this.isDynamicForm()
        );
        if (draftData) {
            touchedFields = _.merge(touchedFields, draftData);
        }

        var params = this.state.editMode ? touchedFields : data;
        if (this.isDynamicForm()) {
            data['attachments'] = this.state.filesBySection;
            data['mic_files'] = this.state.micRecordingsBySection;
            data['camera_files'] = this.state.cameraRecordingsBySection;
            params = data;
            params = this.stripHiddenUploads(
                params,
                this.state.manipulatedDynamicMeta
            );
        }

        if (Object.keys(params).length == 0) {
            //if task updating with that updatetypeid which configured for multiple trigger then allow to update even nothing chnaged in form fields
            const canTriggerUpdateWithoutChanges =
                MyTaskConfigHelper.statusesToAllowFrMultipleTrigger(
                    this.state.viewData?.sbtsk_config_data[0],
                    this.props.updateTypeId
                );
            if (!canTriggerUpdateWithoutChanges) {
                this.setState(
                    {
                        isFormSubmitting: false,
                    },
                    () => {
                        message.info('No change in form');
                    }
                );
                // Nothing to submit
                return;
            }
        }
        params['sbtsk_type_id'] = this.props.editorItem.sbtsk_type.value;
        params['srvc_req_id'] = this.state?.viewData?.form_data?.srvc_req_id;
        params['srvc_type_id'] = this.state?.viewData?.form_data?.srvc_type_id;
        if (this.needToCaptureLocation()) {
            params['user_gps_location_details'] = this.state.locationData;
            params['user_gps_location_text'] =
                this.state.locationData?.address?.text;
            params['user_gps_location_status'] =
                defaultLocationVerificationStatus;
        }
        const onComplete = (resp) => {
            DraftManager.clearDraft(this.draftParams());
            this.setState({
                isFormSubmitting: false,
                error: '',
                visible: false,
            });
            this.tellParentToRefreshList(resp.entry_id);
            this.updateClosureToParent();
            showCreateOrUpdateSuccessMessage();
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        http_utils.performPutCall(
            submitUrl +
                '/' +
                this.props.editorItem.id +
                '/' +
                this.props.updateTypeId,
            params,
            onComplete,
            onError
        );
    };

    reGenerateOtp() {
        this.setState({
            isFormSubmitting: true,
            otpRegenrated: true,
            otp_expiry: Math.floor(Date.now()) + 30000,
        });

        let params = {};
        params['reSendOtp'] = true;
        params['sbtsk_db_id'] = this.props.editorItem.id;
        const onComplete = (resp) => {
            this.setState({
                isFormSubmitting: false,
            });
        };
        const onError = (error) => {
            // compare statuses here
            this.setState({
                isFormSubmitting: false,
                error: http_utils.decodeErrorToMessage(error),
            });
        };
        http_utils.performGetCall(
            reSendOtpUrl + '/' + this.state?.viewData?.form_data?.srvc_req_id,
            params,
            onComplete,
            onError
        );
    }

    onOTPCountDownFinish() {
        this.setState({
            render_helper: !this.state.render_helper,
        });
    }
    getRemarkFieldFrMeta(configuredColSpan) {
        return {
            key: `remarks`,
            colSpan: configuredColSpan,
            label: 'Remarks',
            widget: 'textarea',
            rules: [
                {
                    required: true,
                },
            ],
        };
    }
    handlePartsUpdate = (updatedItems) => {
        this.setState({ partsData: updatedItems });

        // DraftManager.updateDraft(
        //     this.draftParams(),
        //     //this.state.partsData,
        //     updatedItems,
        //     'sbtsk_parts_consumption'
        // );
    };
    getRequestInfoMeta() {
        const startTimeFrEndTime =
            this.formRef.current?.getFieldValue('sbtsk_start_time');
        const startOfDay =
            this.state.viewData?.sbtsk_config_data[0]?.config_data
                ?.sbtsk_time_slot_lower_limit || '09:00AM';
        const endOfDay =
            this.state.viewData?.sbtsk_config_data[0]?.config_data
                ?.sbtsk_time_slot_upper_limit || '07:00PM';

        let showOtpField =
            this.state?.viewData?.form_data
                ?.configure_consumer_otp_verification;
        let doSrvcReqHasOtpInFormData =
            this.state?.viewData?.form_data?.srvc_req_has_otp;
        let seletedStatusForOtpVerification =
            this.state?.viewData?.form_data
                ?.selected_status_for_otp_verification;
        let selectedSrvcTypeIdForOtpVerification =
            this.state?.viewData?.form_data
                ?.selected_srvc_type_id_for_otp_verification;
        let srvc_type_id_of_sbtsk =
            this.state?.viewData?.form_data?.srvc_type_id;
        let otp_field_label = this.props?.editorItem?.otp_field_label || 'OTP';
        const updateTypeDetails = this.getUpdateTypeDetails();
        const statusConfiguredFrParts =
            this.state.viewData?.sbtsk_config_data[0]?.config_data
                ?.statues_for_parts_consumption || [];
        let isStatusConfiguredFrParts = false;
        if (statusConfiguredFrParts.includes(this.props.updateTypeId)) {
            isStatusConfiguredFrParts = true;
        }
        const postponeFields = [
            {
                key: 'sbtsk_start_day',
                label: (
                    <b>
                        <i className="icon icon-calendar gx-mr-2"></i>Start date
                    </b>
                ),
                widget: 'date-picker',
                required: true,
                colSpan: 3,
                widgetProps: {
                    disabledDate: disabledDate,
                    style: {
                        width: '100%',
                    },
                    onChange: (value, dateString) => {
                        this.formRef.current.setFieldsValue({
                            sbtsk_start_day: moment.utc(dateString),
                        });
                    },
                },
            },
            {
                key: 'label2',
                colSpan: 4,
                render() {
                    return <></>;
                },
            },
            {
                key: 'sbtsk_start_time',
                label: (
                    <b>
                        <i className="icon icon-timepicker gx-mr-2"></i>Start
                        Time
                    </b>
                ),
                widget: TimePickerWidget,
                required: true,
                widgetProps: {
                    beginLimit: startOfDay,
                    endLimit: endOfDay,
                    step: 15,
                    onChange: (value) => {
                        this.setState({
                            render_helper: !this.state.render_helper,
                        });
                    },
                },
                colSpan: 2,
            },
            {
                key: 'sbtsk_end_time',
                label: (
                    <b>
                        <i className="icon icon-timepicker gx-mr-2"></i>End Time
                    </b>
                ),
                widget: TimePickerWidget,
                required: true,
                widgetProps: {
                    beginLimit: startTimeFrEndTime
                        ? startTimeFrEndTime
                        : startOfDay,
                    endLimit: endOfDay,
                    step: 15,
                },
                colSpan: 2,
            },
        ];

        let configuredColSpan =
            this.props.updateTypeDetails?.config_data?.[
                `subtask_status_${this.props.updateTypeId}_fields_colspan`
            ] || 4;
        const showRemarksAtBottom =
            this.props.updateTypeDetails?.config_data?.[
                `move_remarks_field_to_bottom_for_${this.props.updateTypeId}`
            ];
        const meta = {
            columns: 1,
            formItemLayout: null,
            fields: [
                //check
                ...(isStatusConfiguredFrParts ? [this.getPartsData()] : []),
                ...this.getSpecificFieldsFrStatus().fields,
                ...(this.props.updateTypeId == 'sbtsk_can_postpone'
                    ? postponeFields
                    : []),
                ...(!showRemarksAtBottom
                    ? [this.getRemarkFieldFrMeta(configuredColSpan)]
                    : []),
            ],
        };

        if (
            showOtpField &&
            doSrvcReqHasOtpInFormData &&
            updateTypeDetails?.value == seletedStatusForOtpVerification &&
            selectedSrvcTypeIdForOtpVerification?.includes(
                srvc_type_id_of_sbtsk
            )
        ) {
            meta.fields.push(
                {
                    key: `otp`,
                    colSpan: 4,
                    label: `Enter ${otp_field_label}`,
                    widget: 'input',
                    rules: [
                        {
                            pattern: new RegExp('^[0-9]{4}$'),
                            message: 'Please enter 4 digit number only!',
                            required: true,
                        },
                    ],
                    ...(this.state.TMS251008278472 && {
                        extra: 'Your Closure code is 1111',
                    }),
                },
                {
                    colSpan: 4,
                    render: () => {
                        return (
                            <>
                                {this.state.otp_expiry &&
                                this.state.otp_expiry >
                                    Math.floor(Date.now()) ? (
                                    <span>
                                        Retry in{' '}
                                        <Countdown
                                            className="gx-d-inline-block"
                                            title={null}
                                            format="ss"
                                            value={this.state.otp_expiry}
                                            onFinish={() => {
                                                this.onOTPCountDownFinish();
                                            }}
                                        />
                                    </span>
                                ) : (
                                    this.state.TMS251008278472 === false && (
                                        <Button
                                            onClick={() => this.reGenerateOtp()}
                                            type="primary"
                                            className="gx-btn-outline-info m-2"
                                        >
                                            <UndoOutlined />{' '}
                                            {`Resend ${otp_field_label}`}
                                        </Button>
                                    )
                                )}
                                {this.state.otpRegenrated && (
                                    <p>
                                        OTP has been sent to Customer Mobile no
                                    </p>
                                )}
                            </>
                        );
                    },
                }
            );
        }
        if (showRemarksAtBottom) {
            meta.fields.push(this.getRemarkFieldFrMeta(configuredColSpan));
        }

        return meta;
    }

    getPartsData() {
        return {
            key: 'sbtsk_parts_consumption',
            colSpan: 1,
            render: () => {
                return (
                    <>
                        <PartsEditor
                            itemEditorData={
                                this.state.viewData?.form_data?.parts_data?.data
                                    ?.data
                            }
                            onPartsUpdate={this.handlePartsUpdate}
                            showPrice={
                                this.state.viewData?.sbtsk_config_data[0]
                                    ?.config_data?.show_part_price_to_assignee
                            }
                            formRef={this.formRef}
                        />
                    </>
                );
            },
        };
    }

    isDynamicForm() {
        return this.props.updateTypeDetails?.config_data?.[
            `sbtsk_status_enable_dynamic_form_for_${this.props.updateTypeId}_status`
        ];
    }

    getCustomFieldsJsonFrmConfig() {
        if (this.state.manipulatedDynamicMeta) {
            return JSON.stringify({
                translatedFields: this.state.manipulatedDynamicMeta.filter(
                    (singleFieldMeta) => singleFieldMeta.hide != true
                ),
            });
        }
        return getCustomFieldsJsonFrStatus(
            this.props.updateTypeId,
            this.props.updateTypeDetails?.config_data
        );
    }

    getSpecificFieldsFrStatus() {
        let customFields = decodeFieldsMetaFrmJson(
            this.getCustomFieldsJsonFrmConfig(),
            undefined,
            false,
            true,
            this.formRef,
            () => {
                if (this.isDynamicForm()) {
                    this.onFormValueChanged(
                        {},
                        this.formRef.current.getFieldsValue()
                    );
                } else {
                    console.log('Not a dynamic form');
                }
            },
            false,
            this.bleComponentProps(),
            (fieldId) => this.onCustomButtonClick(fieldId)
        );
        // console.log('Custom fields for status',customFields.map(fieldMeta=>{fieldMeta.colSpan=1; return fieldMeta}));
        const meta = {
            columns: 4,
            formItemLayout: null,
            fields:
                customFields.length > 0
                    ? [
                          // {
                          //     key: 'specific_details_fr_status',
                          //     colSpan: 4,
                          //     render() {
                          //     return (
                          //         <fieldset>
                          //         <legend><b>Specific details</b></legend>
                          //         </fieldset>
                          //     )
                          //     },
                          // },
                          ...customFields.map((fieldMeta) => {
                              fieldMeta.colSpan = fieldMeta.colSpan || 4;
                              return fieldMeta;
                          }),
                      ]
                    : [],
        };
        return meta;
    }

    getUpdateTypeDetails() {
        let updateTypeId = this.props.updateTypeId;
        let statuses = this.props.updateTypeDetails?.statuses;
        let returnUpdateTypeDetails;
        if (statuses)
            statuses.map((singleStatus) => {
                if (singleStatus.value == updateTypeId) {
                    returnUpdateTypeDetails = singleStatus;
                }
            });
        return returnUpdateTypeDetails;
    }

    onFilesChanged(section, files, uploadedFiles) {
        let newFilesBySection = this.state.filesBySection;
        newFilesBySection[section] = files;
        //removing state as we are handling this with draft
        // this.setState({
        //     filesBySection : newFilesBySection
        // })
        let uploadedFilesBySection = this.state.uploadedFilesBySection;
        uploadedFilesBySection[section] = uploadedFiles;

        this.setState({
            uploadedFilesBySection,
        });
        let attachments = newFilesBySection;
        DraftManager.updateDraft(
            this.draftParams(),
            attachments,
            'attachments'
        );
    }

    onMicFilesChanged(section, files) {
        let newFilesBySection = this.state.micRecordingsBySection;
        newFilesBySection[section] = files;
        //removing state as we are handling this with draft
        // this.setState({
        //     micRecordingsBySection : newFilesBySection
        // })
        let micFiles = newFilesBySection;
        DraftManager.updateDraft(this.draftParams(), micFiles, 'mic_files');
    }

    onCameraFilesChanged = async (section, files) => {
        let newFilesBySection = this.state.cameraRecordingsBySection;
        newFilesBySection[section] = files;
        //removing state as we are handling this with draft
        // this.setState({
        //     cameraRecordingsBySection : newFilesBySection
        // })
        let cameraFiles = newFilesBySection;
        DraftManager.updateDraft(
            this.draftParams(),
            cameraFiles,
            'camera_files'
        );

        // Perform face verification immediately when camera files are changed
        if (this.isSelfieVerificationEnabled() && files && files.length > 0) {
            console.log(
                '🔄 Camera files changed, performing face verification...'
            );

            // Set verification status to loading
            this.setState({
                faceVerificationStatus: 'loading',
                faceVerificationMessage: 'Verifying face...',
            });

            try {
                const faceVerificationResult =
                    await this.performFaceVerification();

                if (faceVerificationResult.success) {
                    this.setState({
                        faceVerificationStatus: 'success',
                        faceVerificationMessage: 'Face verification successful',
                    });
                    message.success('Face verification successful');
                    console.log('✅ Face verification completed successfully');
                } else {
                    this.setState({
                        faceVerificationStatus: 'failed',
                        faceVerificationMessage: faceVerificationResult.message,
                    });
                    message.error(faceVerificationResult.message);
                    console.error(
                        '❌ Face verification failed:',
                        faceVerificationResult.message
                    );
                }
            } catch (error) {
                console.error('Face verification error:', error);
                const errorMessage =
                    'Face verification failed. Please try again.';
                this.setState({
                    faceVerificationStatus: 'failed',
                    faceVerificationMessage: errorMessage,
                });
                message.error(errorMessage);
            }
        }
    };

    onFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({
            sectionWiseUploaderReady: newSectionWiseReady,
        });
    }

    onMicFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseMicUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({
            sectionWiseMicUploaderReady: newSectionWiseReady,
        });
    }

    onCameraFileUploaderReadyChange(section, isReady) {
        let newSectionWiseReady = this.state.sectionWiseCameraUploaderReady;
        newSectionWiseReady[section] = isReady;
        this.setState({
            sectionWiseCameraUploaderReady: newSectionWiseReady,
        });
    }

    getAllFileUploadersReady() {
        let {
            sectionWiseUploaderReady,
            sectionWiseMicUploaderReady,
            sectionWiseCameraUploaderReady,
        } = this.state;
        let notReady = false;
        Object.keys(sectionWiseUploaderReady).map((section) => {
            if (!sectionWiseUploaderReady[section]) {
                notReady = true;
            }
        });
        Object.keys(sectionWiseMicUploaderReady).map((section) => {
            if (!sectionWiseMicUploaderReady[section]) {
                notReady = true;
            }
        });
        Object.keys(sectionWiseCameraUploaderReady).map((section) => {
            if (!sectionWiseCameraUploaderReady[section]) {
                notReady = true;
            }
        });
        return !notReady;
    }

    draftParams = () => {
        return {
            sbtsk_id: this.props?.editorItem?.id,
            update_type_id: this.props?.updateTypeId,
            state: {
                filesBySection: this.state.filesBySection,
                micRecordingsBySection: this.state.micRecordingsBySection,
                cameraRecordingsBySection: this.state.cameraRecordingsBySection,
            },
            fieldValues: this.formRef?.current?.getFieldsValue(),
            isDynamicForm: this.isDynamicForm(),
            hasFeatureAccess: this.state.TMS250617437781,
        };
    };

    removeUploadedFilesFromDraft = (draft, uploadedFiles) => {
        const newDraft = JSON.parse(JSON.stringify(draft)); // Create a deep copy of draft

        for (const key in newDraft.attachments) {
            if (newDraft.attachments.hasOwnProperty(key)) {
                newDraft.attachments[key] = newDraft.attachments[key].filter(
                    (file) => {
                        return !uploadedFiles[key]?.includes(file);
                    }
                );
            }
        }
        return newDraft;
    };

    render() {
        const { editorItem } = this.props;
        const {
            isFormSubmitting,
            visible,
            isLoadingViewData,
            error,
            viewData,
            currentStep,
            srvcDetails,
            fileSections,
            micSections,
            cameraSections,
            bleSections,
            isLoadingLocation,
            locationData,
            isExecutingDynamicFormLogic,
            disableSubmissionButton,
            isDynamicFormFailed,
        } = this.state;
        // console.log("Task update editor props - ",this.props);
        const updateTypeDetails = this.getUpdateTypeDetails();

        const mandatoryStatusReq =
            viewData?.form_data?.mandatory_status == null
                ? []
                : viewData?.form_data?.mandatory_status;

        let editorTitle = 'Update task';
        let editorColor = '#e1e1e1';
        let isFormFilledByDraft = false;
        // For dynamic forms, force apply draft data to get it before dynamic logic executes
        let draftData = this.isDynamicForm()
            ? DraftManager.applyDraft(this.draftParams(), true)
            : DraftManager.applyDraft(this.draftParams());
        if (draftData && Object.keys(draftData).length > 0) {
            isFormFilledByDraft = true;
        }
        let updatedDraftData = {
            ...this.state.viewData?.form_data?.form_data,
            ...draftData,
        };
        updatedDraftData = this.removeUploadedFilesFromDraft(
            updatedDraftData,
            this.state.uploadedFilesBySection
        );
        let prefillFormData = convertDateFieldsToMoments(
            updatedDraftData,
            this.getRequestInfoMeta().fields
        );
        const allFileUploadersReady = this.getAllFileUploadersReady();

        if (updateTypeDetails) {
            editorTitle = 'Update task as ' + updateTypeDetails.title;
            editorColor = updateTypeDetails.color;
        } else if (
            this.props.updateTypeId == 'sbtsk_can_postpone' ||
            this.props.updateTypeId == 'sbtsk_can_reject'
        ) {
            editorTitle =
                this.props.updateTypeId == 'sbtsk_can_postpone'
                    ? 'Postpone task'
                    : 'Reject task';
            editorColor =
                this.props.updateTypeId == 'sbtsk_can_postpone'
                    ? '#13c2c2'
                    : '#f5222d';
            let attachments = prefillFormData?.attachments;
            prefillFormData = {};
            prefillFormData['attachments'] = attachments ? attachments : {};
        }
        if (!this.state.TMS250523533568) {
            setRemarkFieldAsNoRemarkIfEmpty(prefillFormData, this.formRef);
        }

        return (
            <Modal
                title={
                    <span>
                        <i
                            style={{ color: editorColor }}
                            className={`icon icon-circle gx-mr-2 gx-mt-2 gx-vertical-align-middle`}
                        />
                        {editorTitle}
                    </span>
                }
                visible={visible}
                onOk={this.handleOk}
                confirmLoading={isFormSubmitting}
                width={1500}
                style={{
                    marginTop: '-70px',
                }}
                bodyStyle={{
                    minHeight: '85vh',
                    padding: '18px',
                    paddingTop: '0px',
                }}
                footer={null}
                onCancel={this.handleCancel}
                destroyOnClose
                afterClose={() => {
                    const shouldSave = DraftManager.applyDraft(
                        this.draftParams(),
                        this.isDynamicForm()
                    );
                    if (shouldSave) this.showDraftSavedPopUp();
                }}
            >
                {isLoadingViewData ? (
                    <div className="gx-loader-view gx-loader-position">
                        <CircularProgress />
                    </div>
                ) : viewData == undefined ? (
                    <p className="gx-text-red">{error}</p>
                ) : (
                    <>
                        <Row className="gx-mt-2">
                            <Col
                                xs={24}
                                md={14}
                                className="gx-border-right gx-mt-2"
                            >
                                {mandatoryStatusReq.length > 0 ? (
                                    <div>
                                        <h5 className="gx-text-danger">
                                            Please update following statuses
                                            first -{' '}
                                            {this.mandatoryStatusRequirement(
                                                mandatoryStatusReq
                                            )}
                                        </h5>
                                    </div>
                                ) : (
                                    <></>
                                )}

                                <div className="gx-no-header-horizontal-top gx-d-block gx-py-2 gx-px-3">
                                    <div className=" gx-text-grey">
                                        <div className="gx-mb-2">
                                            Assigned by - {editorItem.c_by} -{' '}
                                            <span>
                                                <TaskTime item={editorItem} />
                                            </span>
                                        </div>
                                        {editorItem.title}
                                    </div>
                                    <div className="gx-ml-2 gx-border-left gx-border-grey ">
                                        <Collapse
                                            ghost
                                            // expandIconPosition="right"
                                        >
                                            <Collapse.Panel
                                                header={
                                                    editorItem.srvc_req_details
                                                        .description
                                                }
                                            >
                                                <div>
                                                    <TaskSummaryView
                                                        item={editorItem}
                                                        sbtsk_config_data={
                                                            this.state.viewData
                                                                ?.sbtsk_config_data
                                                                .length > 0
                                                                ? this.state
                                                                      .viewData
                                                                      ?.sbtsk_config_data[0]
                                                                : {}
                                                        }
                                                    />
                                                </div>
                                            </Collapse.Panel>
                                        </Collapse>
                                    </div>
                                </div>
                                {isFormFilledByDraft && (
                                    <Alert
                                        className="gx-mt-4"
                                        message="Unsaved changes, submit form to apply changes"
                                        type="error"
                                        showIcon
                                    />
                                )}
                                <Form
                                    // {...formItemLayout}
                                    className="gx-w-100 gx-mt-3"
                                    layout="vertical"
                                    initialValues={prefillFormData}
                                    ref={this.formRef}
                                    onFinish={(data) => {
                                        DraftManager.updateDraft(
                                            this.draftParams()
                                        );
                                        this.submitForm(data);
                                    }}
                                    disabled={isExecutingDynamicFormLogic}
                                    onValuesChange={(
                                        changedValues,
                                        allValues
                                    ) => {
                                        if (this.isDynamicForm()) {
                                            this.onFormValueChanged(
                                                changedValues,
                                                allValues
                                            );
                                        } else {
                                            DraftManager.updateDraft(
                                                this.draftParams()
                                            );
                                            console.log('Not a dynamic form');
                                        }
                                    }}
                                >
                                    {isExecutingDynamicFormLogic && (
                                        <>
                                            <div className="spin_progress">
                                                <Spin />
                                            </div>
                                        </>
                                    )}
                                    <Row
                                        style={{
                                            flexDirection: 'inherit',
                                        }}
                                    >
                                        <Col xs={24} className="gx-pl-0">
                                            <FormBuilder
                                                key="info"
                                                meta={this.getRequestInfoMeta()}
                                                form={this.formRef}
                                            />
                                        </Col>
                                        <Col
                                            xs={24}
                                            md={24}
                                            className="gx-pl-0"
                                        >
                                            {micSections.map(
                                                (singleMicSection, index) => (
                                                    <Col
                                                        xs={24}
                                                        md={24}
                                                        className="gx-pl-0"
                                                        key={
                                                            singleMicSection.key
                                                        }
                                                    >
                                                        {singleMicSection.title !=
                                                            '' && (
                                                            <h3 className="gx-mt-3">
                                                                {
                                                                    singleMicSection.title
                                                                }
                                                                <hr className="gx-bg-dark"></hr>
                                                            </h3>
                                                        )}
                                                        {/* For debugging */}
                                                        {/* {
                                                                this.state.micRecordingsBySection && 
                                                                <Alert 
                                                                    message={JSON.stringify(prefillFormData.mic_files?.[singleMicSection.key])}
                                                                    type="info" />
                                                            } */}
                                                        <MicInputV2
                                                            authToken={http_utils.getAuthToken()}
                                                            prefixDomain={http_utils.getCDNDomain()}
                                                            initialFiles={
                                                                this.state
                                                                    .editMode
                                                                    ? prefillFormData
                                                                          ?.mic_files?.[
                                                                          singleMicSection
                                                                              .key
                                                                      ]
                                                                    : []
                                                            }
                                                            onFilesChanged={(
                                                                files
                                                            ) => {
                                                                this.onMicFilesChanged(
                                                                    singleMicSection.key,
                                                                    files
                                                                );
                                                            }}
                                                            onReadyStatusChange={(
                                                                isReady
                                                            ) => {
                                                                this.onMicFileUploaderReadyChange(
                                                                    singleMicSection.key,
                                                                    isReady
                                                                );
                                                            }}
                                                        />
                                                    </Col>
                                                )
                                            )}
                                        </Col>
                                        <Col
                                            xs={24}
                                            md={24}
                                            className="gx-pl-0"
                                        >
                                            {cameraSections.map(
                                                (
                                                    singleCameraSection,
                                                    index
                                                ) => (
                                                    <Col
                                                        xs={24}
                                                        md={24}
                                                        className="gx-pl-0"
                                                        key={
                                                            singleCameraSection.key
                                                        }
                                                    >
                                                        {singleCameraSection.title !=
                                                            '' && (
                                                            <>
                                                                <h3 className="gx-mt-3">
                                                                    {singleCameraSection.required && (
                                                                        <span
                                                                            style={{
                                                                                color: 'red',
                                                                            }}
                                                                        >
                                                                            {' '}
                                                                            *{' '}
                                                                        </span>
                                                                    )}
                                                                    {
                                                                        singleCameraSection.title
                                                                    }
                                                                    <hr className="gx-bg-dark"></hr>
                                                                </h3>
                                                            </>
                                                        )}
                                                        {/* For debugging */}
                                                        {/* {
                                                                this.state.micRecordingsBySection && 
                                                                <Alert 
                                                                    message={JSON.stringify(prefillFormData.mic_files?.[singleMicSection.key])}
                                                                    type="info" />
                                                            } */}
                                                        <CameraInput
                                                            ref={this.cameraRef}
                                                            authToken={http_utils.getAuthToken()}
                                                            prefixDomain={http_utils.getCDNDomain()}
                                                            required={
                                                                singleCameraSection.required
                                                            }
                                                            initialFiles={
                                                                this.state
                                                                    .editMode
                                                                    ? prefillFormData
                                                                          ?.camera_files?.[
                                                                          singleCameraSection
                                                                              .key
                                                                      ]
                                                                    : []
                                                            }
                                                            onFilesChanged={(
                                                                files
                                                            ) => {
                                                                this.onCameraFilesChanged(
                                                                    singleCameraSection.key,
                                                                    files
                                                                );
                                                            }}
                                                            onReadyStatusChange={(
                                                                isReady
                                                            ) => {
                                                                this.onCameraFileUploaderReadyChange(
                                                                    singleCameraSection.key,
                                                                    isReady
                                                                );
                                                            }}
                                                        />

                                                        {/* Face Verification Status Display */}
                                                        {this.isSelfieVerificationEnabled() &&
                                                            this.state
                                                                .faceVerificationStatus && (
                                                                <div className="gx-mt-2">
                                                                    {this.state
                                                                        .faceVerificationStatus ===
                                                                        'loading' && (
                                                                        <Alert
                                                                            message="Face Verification"
                                                                            description={
                                                                                this
                                                                                    .state
                                                                                    .faceVerificationMessage
                                                                            }
                                                                            type="info"
                                                                            showIcon
                                                                            icon={
                                                                                <Spin size="small" />
                                                                            }
                                                                        />
                                                                    )}
                                                                    {this.state
                                                                        .faceVerificationStatus ===
                                                                        'success' && (
                                                                        <Alert
                                                                            message="Face Verification Successful"
                                                                            description={
                                                                                this
                                                                                    .state
                                                                                    .faceVerificationMessage
                                                                            }
                                                                            type="success"
                                                                            showIcon
                                                                            closable
                                                                        />
                                                                    )}
                                                                    {this.state
                                                                        .faceVerificationStatus ===
                                                                        'failed' && (
                                                                        <Alert
                                                                            message="Face Verification Failed"
                                                                            description={
                                                                                this
                                                                                    .state
                                                                                    .faceVerificationMessage
                                                                            }
                                                                            type="error"
                                                                            showIcon
                                                                            action={
                                                                                <Button
                                                                                    size="small"
                                                                                    type="primary"
                                                                                    onClick={() => {
                                                                                        // Reset verification status to allow retry
                                                                                        this.setState(
                                                                                            {
                                                                                                faceVerificationStatus:
                                                                                                    null,
                                                                                                faceVerificationMessage:
                                                                                                    '',
                                                                                            }
                                                                                        );
                                                                                    }}
                                                                                >
                                                                                    Try
                                                                                    Again
                                                                                </Button>
                                                                            }
                                                                        />
                                                                    )}
                                                                </div>
                                                            )}
                                                    </Col>
                                                )
                                            )}
                                        </Col>
                                        <Col
                                            xs={24}
                                            md={24}
                                            className="gx-pl-0"
                                        >
                                            {fileSections.map(
                                                (singleFileSection, index) => (
                                                    <Col
                                                        xs={24}
                                                        md={24}
                                                        className="gx-pl-0"
                                                        key={
                                                            singleFileSection.key
                                                        }
                                                    >
                                                        {/* {singleFileSection.title != '' && <p>{singleFileSection.title}</p>} */}
                                                        {singleFileSection.title !=
                                                            '' && (
                                                            <h3 className="gx-mt-3">
                                                                {singleFileSection.required && (
                                                                    <span
                                                                        style={{
                                                                            color: 'red',
                                                                        }}
                                                                    >
                                                                        {' '}
                                                                        *{' '}
                                                                    </span>
                                                                )}
                                                                {
                                                                    singleFileSection.title
                                                                }
                                                                <hr className="gx-bg-dark"></hr>
                                                            </h3>
                                                        )}
                                                        {/* For debugging */}
                                                        {/* {
                                                                this.state.filesBySection && 
                                                                <Alert 
                                                                    message={JSON.stringify(this.state.filesBySection)}
                                                                    type="info" />
                                                            } */}
                                                        <S3Uploader
                                                            // className="gx-w-50"
                                                            // demoMode
                                                            required={
                                                                singleFileSection.required
                                                            }
                                                            maxColSpan={4}
                                                            authToken={http_utils.getAuthToken()}
                                                            prefixDomain={http_utils.getCDNDomain()}
                                                            onFilesChanged={(
                                                                files,
                                                                deletedFileUrl,
                                                                uploadedFiles
                                                            ) => {
                                                                this.onFilesChanged(
                                                                    singleFileSection.key,
                                                                    files,
                                                                    uploadedFiles
                                                                );
                                                            }}
                                                            onReadyStatusChanged={(
                                                                isReady
                                                            ) => {
                                                                this.onFileUploaderReadyChange(
                                                                    singleFileSection.key,
                                                                    isReady
                                                                );
                                                            }}
                                                            initialFiles={
                                                                this.state
                                                                    .editMode
                                                                    ? prefillFormData
                                                                          ?.attachments?.[
                                                                          singleFileSection
                                                                              .key
                                                                      ]
                                                                    : []
                                                            }
                                                            isDraftEnabled
                                                            customPreviewHeight="100%"
                                                            customFileIconMaxWidth="40px"
                                                            compConfig={{
                                                                name: 'task-update-editor-attachments',
                                                            }}
                                                        />
                                                    </Col>
                                                )
                                            )}
                                        </Col>
                                    </Row>
                                    <div className="gx-mt-4">
                                        {this.needToCaptureLocation() && (
                                            <LocationDetails
                                                isLoadingLocation={
                                                    isLoadingLocation
                                                }
                                                locationData={locationData}
                                                onRetry={() => {
                                                    this.initGeoFenceStuff();
                                                }}
                                            />
                                        )}
                                    </div>
                                    <div className="gx-mt-4">
                                        {!allFileUploadersReady && (
                                            <Spin></Spin>
                                        )}
                                        <Button
                                            type="primary"
                                            htmlType="submit"
                                            data-testid="submit-button"
                                            disabled={
                                                isFormSubmitting ||
                                                !allFileUploadersReady ||
                                                mandatoryStatusReq.length > 0 ||
                                                disableSubmissionButton ||
                                                isExecutingDynamicFormLogic ||
                                                isDynamicFormFailed
                                            }
                                        >
                                            {editorTitle}
                                        </Button>

                                        {isFormSubmitting ? (
                                            <div className="gx-loader-view gx-loader-position">
                                                <CircularProgress />
                                            </div>
                                        ) : null}
                                        {error ? (
                                            <p className="gx-text-red">
                                                {error}
                                            </p>
                                        ) : null}
                                    </div>
                                </Form>
                            </Col>
                        </Row>
                    </>
                )}
            </Modal>
        );
    }
}

export default TaskUpdateEditor;
